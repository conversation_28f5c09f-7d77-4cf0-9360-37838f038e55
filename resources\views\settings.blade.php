@extends('layouts.master')

@section('content')
<div class="row">
    <div class="col-12">
        <div class="page-title-box d-sm-flex align-items-center justify-content-between">
            <h4 class="mb-sm-0">Settings</h4>

            <div class="page-title-right">
                <ol class="breadcrumb m-0">
                    <li class="breadcrumb-item"><a href="javascript: void(0);">Pages</a></li>
                    <li class="breadcrumb-item active">Settings</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-xxl-3">
        <div class="card">
            <div class="card-body p-4">
                <div class="text-center">
                    <div class="profile-user position-relative d-inline-block mx-auto mb-4">
                        <img src="{{ asset('assets/images/users/avatar-1.jpg') }}" class="rounded-circle avatar-xl img-thumbnail user-profile-image material-shadow" alt="user-profile-image">
                        <div class="avatar-xs p-0 rounded-circle profile-photo-edit">
                            <input id="profile-img-file-input" type="file" class="profile-img-file-input">
                            <label for="profile-img-file-input" class="profile-photo-edit avatar-xs">
                                <span class="avatar-title rounded-circle bg-light text-body material-shadow">
                                    <i class="ri-camera-fill"></i>
                                </span>
                            </label>
                        </div>
                    </div>
                    <h5 class="fs-16 mb-1">Anna Adame</h5>
                    <p class="text-muted mb-0">Lead Designer / Developer</p>
                </div>
            </div>
        </div>
        <!--end card-->

        <div class="card">
            <div class="card-body">
                <div class="d-flex align-items-center mb-4">
                    <div class="flex-grow-1">
                        <h5 class="card-title mb-0">Settings Menu</h5>
                    </div>
                </div>
                <div class="list-group list-group-flush">
                    <a href="#general-settings" class="list-group-item list-group-item-action active" data-bs-toggle="list">
                        <i class="ri-settings-3-line me-3"></i>General Settings
                    </a>
                    <a href="#security-settings" class="list-group-item list-group-item-action" data-bs-toggle="list">
                        <i class="ri-shield-keyhole-line me-3"></i>Security
                    </a>
                    <a href="#notification-settings" class="list-group-item list-group-item-action" data-bs-toggle="list">
                        <i class="ri-notification-3-line me-3"></i>Notifications
                    </a>
                    <a href="#privacy-settings" class="list-group-item list-group-item-action" data-bs-toggle="list">
                        <i class="ri-lock-line me-3"></i>Privacy
                    </a>
                    <a href="#billing-settings" class="list-group-item list-group-item-action" data-bs-toggle="list">
                        <i class="ri-bill-line me-3"></i>Billing
                    </a>
                </div>
            </div>
        </div>
        <!--end card-->
    </div>
    <!--end col-->

    <div class="col-xxl-9">
        <div class="card">
            <div class="card-body p-4">
                <div class="tab-content">
                    <div class="tab-pane fade show active" id="general-settings">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="mb-3">
                                    <h5 class="card-title text-decoration-underline mb-3">General Settings:</h5>
                                </div>
                            </div>
                            <!--end col-->
                        </div>
                        <!--end row-->

                        <form action="javascript:void(0);">
                            <div class="row">
                                <div class="col-lg-6">
                                    <div class="mb-3">
                                        <label for="websiteInput" class="form-label">Website</label>
                                        <input type="url" class="form-control" id="websiteInput" placeholder="www.example.com" value="www.annaadame.com" />
                                    </div>
                                </div>
                                <!--end col-->
                                <div class="col-lg-6">
                                    <div class="mb-3">
                                        <label for="timezoneInput" class="form-label">Timezone</label>
                                        <select class="form-control" data-choices name="timezoneInput" id="timezoneInput">
                                            <option value="">Select Timezone</option>
                                            <option value="Pacific/Midway">(UTC-11:00) Midway Island, Samoa</option>
                                            <option value="America/Adak">(UTC-10:00) Hawaii-Aleutian</option>
                                            <option value="Etc/GMT+10">(UTC-10:00) Hawaii</option>
                                            <option value="Pacific/Marquesas">(UTC-09:30) Marquesas Islands</option>
                                            <option value="Pacific/Gambier">(UTC-09:00) Gambier Islands</option>
                                            <option value="America/Anchorage">(UTC-09:00) Alaska</option>
                                            <option value="America/Ensenada">(UTC-08:00) Tijuana, Baja California</option>
                                            <option value="Etc/GMT+8">(UTC-08:00) Pitcairn Islands</option>
                                            <option value="America/Los_Angeles" selected>(UTC-08:00) Pacific Time (US & Canada)</option>
                                            <option value="America/Denver">(UTC-07:00) Mountain Time (US & Canada)</option>
                                            <option value="America/Chihuahua">(UTC-07:00) Chihuahua, La Paz, Mazatlan</option>
                                            <option value="America/Dawson_Creek">(UTC-07:00) Arizona</option>
                                            <option value="America/Belize">(UTC-06:00) Saskatchewan, Central America</option>
                                            <option value="America/Cancun">(UTC-06:00) Guadalajara, Mexico City, Monterrey</option>
                                            <option value="Chile/EasterIsland">(UTC-06:00) Easter Island</option>
                                            <option value="America/Chicago">(UTC-06:00) Central Time (US & Canada)</option>
                                            <option value="America/New_York">(UTC-05:00) Eastern Time (US & Canada)</option>
                                            <option value="America/Havana">(UTC-05:00) Cuba</option>
                                            <option value="America/Bogota">(UTC-05:00) Bogota, Lima, Quito, Rio Branco</option>
                                            <option value="America/Caracas">(UTC-04:30) Caracas</option>
                                            <option value="America/Santiago">(UTC-04:00) Santiago</option>
                                            <option value="America/La_Paz">(UTC-04:00) La Paz</option>
                                            <option value="Atlantic/Stanley">(UTC-04:00) Faukland Islands</option>
                                            <option value="America/Campo_Grande">(UTC-04:00) Brazil</option>
                                            <option value="America/Goose_Bay">(UTC-04:00) Atlantic Time (Goose Bay)</option>
                                            <option value="America/Glace_Bay">(UTC-04:00) Atlantic Time (Canada)</option>
                                            <option value="America/St_Johns">(UTC-03:30) Newfoundland</option>
                                            <option value="America/Araguaina">(UTC-03:00) UTC-3</option>
                                            <option value="America/Montevideo">(UTC-03:00) Montevideo</option>
                                            <option value="America/Miquelon">(UTC-03:00) Miquelon, St. Pierre</option>
                                            <option value="America/Godthab">(UTC-03:00) Greenland</option>
                                            <option value="America/Argentina/Buenos_Aires">(UTC-03:00) Buenos Aires</option>
                                            <option value="America/Sao_Paulo">(UTC-03:00) Brasilia</option>
                                            <option value="America/Noronha">(UTC-02:00) Mid-Atlantic</option>
                                            <option value="Atlantic/Cape_Verde">(UTC-01:00) Cape Verde Is.</option>
                                            <option value="Atlantic/Azores">(UTC-01:00) Azores</option>
                                            <option value="Europe/Belfast">(UTC+00:00) Greenwich Mean Time : Belfast</option>
                                            <option value="Europe/Dublin">(UTC+00:00) Greenwich Mean Time : Dublin</option>
                                            <option value="Europe/Lisbon">(UTC+00:00) Greenwich Mean Time : Lisbon</option>
                                            <option value="Europe/London">(UTC+00:00) Greenwich Mean Time : London</option>
                                            <option value="Africa/Abidjan">(UTC+00:00) Monrovia, Reykjavik</option>
                                            <option value="Europe/Amsterdam">(UTC+01:00) Amsterdam, Berlin, Bern, Rome, Stockholm, Vienna</option>
                                            <option value="Europe/Belgrade">(UTC+01:00) Belgrade, Bratislava, Budapest, Ljubljana, Prague</option>
                                            <option value="Europe/Brussels">(UTC+01:00) Brussels, Copenhagen, Madrid, Paris</option>
                                        </select>
                                    </div>
                                </div>
                                <!--end col-->
                                <div class="col-lg-6">
                                    <div class="mb-3">
                                        <label for="languageInput" class="form-label">Language</label>
                                        <select class="form-control" data-choices name="languageInput" id="languageInput">
                                            <option value="">Select Language</option>
                                            <option value="english" selected>English</option>
                                            <option value="spanish">Spanish</option>
                                            <option value="french">French</option>
                                            <option value="german">German</option>
                                            <option value="italian">Italian</option>
                                            <option value="russian">Russian</option>
                                            <option value="chinese">Chinese</option>
                                            <option value="japanese">Japanese</option>
                                            <option value="korean">Korean</option>
                                        </select>
                                    </div>
                                </div>
                                <!--end col-->
                                <div class="col-lg-6">
                                    <div class="mb-3">
                                        <label for="currencyInput" class="form-label">Currency</label>
                                        <select class="form-control" data-choices name="currencyInput" id="currencyInput">
                                            <option value="">Select Currency</option>
                                            <option value="USD" selected>USD - US Dollar</option>
                                            <option value="EUR">EUR - Euro</option>
                                            <option value="GBP">GBP - British Pound</option>
                                            <option value="JPY">JPY - Japanese Yen</option>
                                            <option value="CAD">CAD - Canadian Dollar</option>
                                            <option value="AUD">AUD - Australian Dollar</option>
                                            <option value="CHF">CHF - Swiss Franc</option>
                                            <option value="CNY">CNY - Chinese Yuan</option>
                                            <option value="INR">INR - Indian Rupee</option>
                                        </select>
                                    </div>
                                </div>
                                <!--end col-->
                                <div class="col-lg-12">
                                    <div class="mb-3">
                                        <label for="descriptionInput" class="form-label">Description</label>
                                        <textarea class="form-control" id="descriptionInput" placeholder="Enter your description" rows="3">Hi I'm Anna Adame,It will be as simple as Occidental; in fact, it will be Occidental. To an English person, it will seem like simplified English, as a skeptical Cambridge friend of mine told me what Occidental is European languages are members of the same family.</textarea>
                                    </div>
                                </div>
                                <!--end col-->
                                <div class="col-lg-12">
                                    <div class="hstack gap-2 justify-content-end">
                                        <button type="submit" class="btn btn-primary">Save Changes</button>
                                        <button type="button" class="btn btn-soft-success">Cancel</button>
                                    </div>
                                </div>
                                <!--end col-->
                            </div>
                            <!--end row-->
                        </form>
                    </div>
                    <!--end tab-pane-->

                    <div class="tab-pane fade" id="security-settings">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="mb-3">
                                    <h5 class="card-title text-decoration-underline mb-3">Security Settings:</h5>
                                </div>
                            </div>
                            <!--end col-->
                        </div>
                        <!--end row-->

                        <div class="mb-4 pb-2">
                            <div class="d-flex flex-column flex-sm-row mb-4 mb-sm-0">
                                <div class="flex-grow-1">
                                    <h6 class="fs-14 mb-1">Two-factor Authentication</h6>
                                    <p class="text-muted">Two-factor authentication is an enhanced security meansur. Once enabled, you'll be required to give two types of identification when you log into Google Authentication and SMS are Supported.</p>
                                </div>
                                <div class="flex-shrink-0 ms-sm-3">
                                    <a href="javascript:void(0);" class="btn btn-sm btn-primary">Enable Two-facor Authentication</a>
                                </div>
                            </div>
                            <div class="d-flex flex-column flex-sm-row mb-4 mb-sm-0 mt-3">
                                <div class="flex-grow-1">
                                    <h6 class="fs-14 mb-1">Secondary Verification</h6>
                                    <p class="text-muted">The first factor is a password and the second commonly includes a text with a code sent to your smartphone, or biometrics using your fingerprint, face, or retina.</p>
                                </div>
                                <div class="flex-shrink-0 ms-sm-3">
                                    <a href="javascript:void(0);" class="btn btn-sm btn-primary">Set up secondary method</a>
                                </div>
                            </div>
                            <div class="d-flex flex-column flex-sm-row mb-4 mb-sm-0 mt-3">
                                <div class="flex-grow-1">
                                    <h6 class="fs-14 mb-1">Backup Codes</h6>
                                    <p class="text-muted mb-sm-0">A backup code is automatically generated for you when you turn on two-factor authentication through your iOS or Android Twitter app. You can also generate a backup code on twitter.com.</p>
                                </div>
                                <div class="flex-shrink-0 ms-sm-3">
                                    <a href="javascript:void(0);" class="btn btn-sm btn-primary">Generate backup codes</a>
                                </div>
                            </div>
                        </div>

                        <form action="javascript:void(0);">
                            <div class="row g-2">
                                <div class="col-lg-4">
                                    <div>
                                        <label for="oldpasswordInput" class="form-label">Old Password*</label>
                                        <input type="password" class="form-control" id="oldpasswordInput" placeholder="Enter current password">
                                    </div>
                                </div>
                                <!--end col-->
                                <div class="col-lg-4">
                                    <div>
                                        <label for="newpasswordInput" class="form-label">New Password*</label>
                                        <input type="password" class="form-control" id="newpasswordInput" placeholder="Enter new password">
                                    </div>
                                </div>
                                <!--end col-->
                                <div class="col-lg-4">
                                    <div>
                                        <label for="confirmpasswordInput" class="form-label">Confirm Password*</label>
                                        <input type="password" class="form-control" id="confirmpasswordInput" placeholder="Confirm password">
                                    </div>
                                </div>
                                <!--end col-->
                                <div class="col-lg-12">
                                    <div class="mb-3">
                                        <a href="javascript:void(0);" class="link-primary text-decoration-underline">Forgot Password ?</a>
                                    </div>
                                </div>
                                <!--end col-->
                                <div class="col-lg-12">
                                    <div class="text-end">
                                        <button type="submit" class="btn btn-success">Change Password</button>
                                    </div>
                                </div>
                                <!--end col-->
                            </div>
                            <!--end row-->
                        </form>
                    </div>
                    <!--end tab-pane-->

                    <div class="tab-pane fade" id="notification-settings">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="mb-3">
                                    <h5 class="card-title text-decoration-underline mb-3">Notification Settings:</h5>
                                </div>
                            </div>
                            <!--end col-->
                        </div>
                        <!--end row-->

                        <div class="mb-3">
                            <ul class="list-unstyled mb-0">
                                <li class="d-flex">
                                    <div class="flex-grow-1">
                                        <label for="directMessage" class="form-check-label fs-14">Direct messages</label>
                                        <p class="text-muted">Messages from people you follow</p>
                                    </div>
                                    <div class="flex-shrink-0">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" role="switch" id="directMessage" checked />
                                        </div>
                                    </div>
                                </li>
                                <li class="d-flex mt-2">
                                    <div class="flex-grow-1">
                                        <label class="form-check-label fs-14" for="desktopNotification">
                                            Show desktop notifications
                                        </label>
                                        <p class="text-muted">Choose the option you want as your default setting. Block a site: Next to "Not allowed to send notifications," click Add.</p>
                                    </div>
                                    <div class="flex-shrink-0">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" role="switch" id="desktopNotification" checked />
                                        </div>
                                    </div>
                                </li>
                                <li class="d-flex mt-2">
                                    <div class="flex-grow-1">
                                        <label class="form-check-label fs-14" for="emailNotification">
                                            Show email notifications
                                        </label>
                                        <p class="text-muted"> Under Settings, choose Notifications. Under Select an account, choose the account to enable notifications for. </p>
                                    </div>
                                    <div class="flex-shrink-0">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" role="switch" id="emailNotification" />
                                        </div>
                                    </div>
                                </li>
                                <li class="d-flex mt-2">
                                    <div class="flex-grow-1">
                                        <label class="form-check-label fs-14" for="chatNotification">
                                            Show chat notifications
                                        </label>
                                        <p class="text-muted">To prevent duplicate mobile notifications from the Gmail and Chat apps, in settings, turn off Chat notifications.</p>
                                    </div>
                                    <div class="flex-shrink-0">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" role="switch" id="chatNotification" />
                                        </div>
                                    </div>
                                </li>
                                <li class="d-flex mt-2">
                                    <div class="flex-grow-1">
                                        <label class="form-check-label fs-14" for="purchaesNotification">
                                            Show purchase notifications
                                        </label>
                                        <p class="text-muted">Get real-time purchase alerts to protect yourself from fraudulent charges.</p>
                                    </div>
                                    <div class="flex-shrink-0">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" role="switch" id="purchaesNotification" />
                                        </div>
                                    </div>
                                </li>
                            </ul>
                        </div>

                        <div class="text-end">
                            <button type="submit" class="btn btn-success">Save Changes</button>
                        </div>
                    </div>
                    <!--end tab-pane-->

                    <div class="tab-pane fade" id="privacy-settings">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="mb-3">
                                    <h5 class="card-title text-decoration-underline mb-3">Privacy Settings:</h5>
                                </div>
                            </div>
                            <!--end col-->
                        </div>
                        <!--end row-->

                        <div class="mb-4 pb-2">
                            <div class="d-flex flex-column flex-sm-row mb-4 mb-sm-0">
                                <div class="flex-grow-1">
                                    <h6 class="fs-14 mb-1">Profile Visibility</h6>
                                    <p class="text-muted">Select who can see your profile information</p>
                                </div>
                                <div class="flex-shrink-0 ms-sm-3">
                                    <select class="form-control" data-choices name="profileVisibility" id="profileVisibility">
                                        <option value="public" selected>Public</option>
                                        <option value="friends">Friends Only</option>
                                        <option value="private">Private</option>
                                    </select>
                                </div>
                            </div>
                            <div class="d-flex flex-column flex-sm-row mb-4 mb-sm-0 mt-3">
                                <div class="flex-grow-1">
                                    <h6 class="fs-14 mb-1">Activity Status</h6>
                                    <p class="text-muted">Show when you're active on the platform</p>
                                </div>
                                <div class="flex-shrink-0 ms-sm-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" role="switch" id="activityStatus" checked />
                                    </div>
                                </div>
                            </div>
                            <div class="d-flex flex-column flex-sm-row mb-4 mb-sm-0 mt-3">
                                <div class="flex-grow-1">
                                    <h6 class="fs-14 mb-1">Data Collection</h6>
                                    <p class="text-muted mb-sm-0">Allow us to collect data to improve your experience</p>
                                </div>
                                <div class="flex-shrink-0 ms-sm-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" role="switch" id="dataCollection" />
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="text-end">
                            <button type="submit" class="btn btn-success">Save Changes</button>
                        </div>
                    </div>
                    <!--end tab-pane-->

                    <div class="tab-pane fade" id="billing-settings">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="mb-3">
                                    <h5 class="card-title text-decoration-underline mb-3">Billing Settings:</h5>
                                </div>
                            </div>
                            <!--end col-->
                        </div>
                        <!--end row-->

                        <div class="row">
                            <div class="col-lg-6">
                                <div class="card border">
                                    <div class="card-header">
                                        <h6 class="card-title mb-0">Current Plan</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="d-flex mb-3">
                                            <div class="flex-grow-1">
                                                <h5 class="fs-15">Professional Plan</h5>
                                                <p class="text-muted mb-0">$29/month</p>
                                            </div>
                                            <div class="flex-shrink-0">
                                                <span class="badge bg-success">Active</span>
                                            </div>
                                        </div>
                                        <p class="text-muted">Next billing date: March 15, 2024</p>
                                        <div class="hstack gap-2">
                                            <button type="button" class="btn btn-primary btn-sm">Upgrade Plan</button>
                                            <button type="button" class="btn btn-soft-danger btn-sm">Cancel Plan</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!--end col-->
                            <div class="col-lg-6">
                                <div class="card border">
                                    <div class="card-header">
                                        <h6 class="card-title mb-0">Payment Method</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="d-flex mb-3">
                                            <div class="flex-grow-1">
                                                <h6 class="fs-14 mb-1">Visa ending in 4242</h6>
                                                <p class="text-muted mb-0">Expires 12/2025</p>
                                            </div>
                                            <div class="flex-shrink-0">
                                                <span class="badge bg-light text-body">Default</span>
                                            </div>
                                        </div>
                                        <div class="hstack gap-2">
                                            <button type="button" class="btn btn-primary btn-sm">Update Card</button>
                                            <button type="button" class="btn btn-soft-secondary btn-sm">Add New Card</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!--end col-->
                        </div>
                        <!--end row-->

                        <div class="row">
                            <div class="col-lg-12">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="card-title mb-0">Billing History</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="table-responsive">
                                            <table class="table table-nowrap align-middle mb-0">
                                                <thead>
                                                    <tr>
                                                        <th scope="col">Date</th>
                                                        <th scope="col">Description</th>
                                                        <th scope="col">Amount</th>
                                                        <th scope="col">Status</th>
                                                        <th scope="col">Action</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr>
                                                        <td>Feb 15, 2024</td>
                                                        <td>Professional Plan - Monthly</td>
                                                        <td>$29.00</td>
                                                        <td><span class="badge bg-success">Paid</span></td>
                                                        <td>
                                                            <button type="button" class="btn btn-sm btn-soft-primary">Download</button>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td>Jan 15, 2024</td>
                                                        <td>Professional Plan - Monthly</td>
                                                        <td>$29.00</td>
                                                        <td><span class="badge bg-success">Paid</span></td>
                                                        <td>
                                                            <button type="button" class="btn btn-sm btn-soft-primary">Download</button>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td>Dec 15, 2023</td>
                                                        <td>Professional Plan - Monthly</td>
                                                        <td>$29.00</td>
                                                        <td><span class="badge bg-success">Paid</span></td>
                                                        <td>
                                                            <button type="button" class="btn btn-sm btn-soft-primary">Download</button>
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!--end col-->
                        </div>
                        <!--end row-->
                    </div>
                    <!--end tab-pane-->
                </div>
            </div>
        </div>
    </div>
    <!--end col-->
</div>
<!--end row-->

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle tab switching
    const tabLinks = document.querySelectorAll('.list-group-item-action');
    const tabPanes = document.querySelectorAll('.tab-pane');
    
    tabLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Remove active class from all links and panes
            tabLinks.forEach(l => l.classList.remove('active'));
            tabPanes.forEach(p => {
                p.classList.remove('show', 'active');
            });
            
            // Add active class to clicked link
            this.classList.add('active');
            
            // Show corresponding tab pane
            const targetId = this.getAttribute('href').substring(1);
            const targetPane = document.getElementById(targetId);
            if (targetPane) {
                targetPane.classList.add('show', 'active');
            }
        });
    });
});
</script>
@endsection
