/*! shepherd.js 11.2.0 */

'use strict';(function(K,ra){"object"===typeof exports&&"undefined"!==typeof module?module.exports=ra():"function"===typeof define&&define.amd?define(ra):(K="undefined"!==typeof globalThis?globalThis:K||self,<PERSON><PERSON>=ra())})(this,function(){function K(a,b){return!1!==b.clone&&b.isMergeableObject(a)?fa(Array.isArray(a)?[]:{},a,b):a}function ra(a,b,c){return a.concat(b).map(function(d){return K(d,c)})}function Bb(a){return Object.getOwnPropertySymbols?Object.getOwnPropertySymbols(a).filter(function(b){return Object.propertyIsEnumerable.call(a,
b)}):[]}function Ua(a){return Object.keys(a).concat(Bb(a))}function Va(a,b){try{return b in a}catch(c){return!1}}function Cb(a,b,c){var d={};c.isMergeableObject(a)&&Ua(a).forEach(function(e){d[e]=K(a[e],c)});Ua(b).forEach(function(e){if(!Va(a,e)||Object.hasOwnProperty.call(a,e)&&Object.propertyIsEnumerable.call(a,e))if(Va(a,e)&&c.isMergeableObject(b[e])){if(c.customMerge){var f=c.customMerge(e);f="function"===typeof f?f:fa}else f=fa;d[e]=f(a[e],b[e],c)}else d[e]=K(b[e],c)});return d}function fa(a,
b,c){c=c||{};c.arrayMerge=c.arrayMerge||ra;c.isMergeableObject=c.isMergeableObject||Db;c.cloneUnlessOtherwiseSpecified=K;var d=Array.isArray(b),e=Array.isArray(a);return d!==e?K(b,c):d?c.arrayMerge(a,b,c):Cb(a,b,c)}function X(a){return"function"===typeof a}function sa(a){return"string"===typeof a}function Wa(a){let b=Object.getOwnPropertyNames(a.constructor.prototype);for(let c=0;c<b.length;c++){let d=b[c],e=a[d];"constructor"!==d&&"function"===typeof e&&(a[d]=e.bind(a))}return a}function Eb(a,b){return c=>
{if(b.isOpen()){let d=b.el&&c.currentTarget===b.el;(void 0!==a&&c.currentTarget.matches(a)||d)&&b.tour.next()}}}function Fb(a){let {event:b,selector:c}=a.options.advanceOn||{};if(b){let d=Eb(c,a),e;try{e=document.querySelector(c)}catch(f){}if(void 0===c||e)e?(e.addEventListener(b,d),a.on("destroy",()=>e.removeEventListener(b,d))):(document.body.addEventListener(b,d,!0),a.on("destroy",()=>document.body.removeEventListener(b,d,!0)));else return console.error(`No element was found for the selector supplied to advanceOn: ${c}`)}else return console.error("advanceOn was defined, but no event name was passed.")}
function Xa(a){return sa(a)&&""!==a?"-"!==a.charAt(a.length-1)?`${a}-`:a:""}function Ga(){let a=Date.now();return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,b=>{let c=(a+16*Math.random())%16|0;a=Math.floor(a/16);return("x"==b?c:c&3|8).toString(16)})}function D(){D=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b],d;for(d in c)Object.prototype.hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a};return D.apply(this,arguments)}function Ya(a,
b){if(null==a)return{};var c={},d=Object.keys(a),e;for(e=0;e<d.length;e++){var f=d[e];0<=b.indexOf(f)||(c[f]=a[f])}return c}function ha(a,b){return"function"===typeof a?a(b):a}function Y(a){return a.split("-")[0]}function ta(a){return"x"===a?"y":"x"}function Ha(a){return"y"===a?"height":"width"}function ia(a){return["top","bottom"].includes(Y(a))?"y":"x"}function Gb(a,b,c){void 0===c&&(c=!1);let d=a.split("-")[1],e=ta(ia(a));a=Ha(e);c="x"===e?d===(c?"end":"start")?"right":"left":"start"===d?"bottom":
"top";b.reference[a]>b.floating[a]&&(c=va(c));return[c,va(c)]}function Hb(a){let b=va(a);return[Ia(a),b,Ia(b)]}function Ia(a){return a.replace(/start|end/g,b=>Ib[b])}function Jb(a,b,c){let d=["left","right"],e=["right","left"],f=["top","bottom"],g=["bottom","top"];switch(a){case "top":case "bottom":return c?b?e:d:b?d:e;case "left":case "right":return b?f:g;default:return[]}}function Kb(a,b,c,d){let e=a.split("-")[1];a=Jb(Y(a),"start"===c,d);e&&(a=a.map(f=>f+"-"+e),b&&(a=a.concat(a.map(Ia))));return a}
function va(a){return a.replace(/left|right|bottom|top/g,b=>Lb[b])}function Za(a){return"number"!==typeof a?D({top:0,right:0,bottom:0,left:0},a):{top:a,right:a,bottom:a,left:a}}function wa(a){return D({},a,{top:a.y,left:a.x,right:a.x+a.width,bottom:a.y+a.height})}function $a(a,b,c){let {reference:d,floating:e}=a;var f=ia(b);a=ta(ia(b));var g=Ha(a),h=Y(b);f="y"===f;let l=d.x+d.width/2-e.width/2,k=d.y+d.height/2-e.height/2;g=d[g]/2-e[g]/2;switch(h){case "top":h={x:l,y:d.y-e.height};break;case "bottom":h=
{x:l,y:d.y+d.height};break;case "right":h={x:d.x+d.width,y:k};break;case "left":h={x:d.x-e.width,y:k};break;default:h={x:d.x,y:d.y}}switch(b.split("-")[1]){case "start":h[a]-=g*(c&&f?-1:1);break;case "end":h[a]+=g*(c&&f?-1:1)}return h}async function ab(a,b){var c;void 0===b&&(b={});let {x:d,y:e,platform:f,rects:g,elements:h,strategy:l}=a,{boundary:k="clippingAncestors",rootBoundary:m="viewport",elementContext:q="floating",altBoundary:p=!1,padding:u=0}=ha(b,a);a=Za(u);b=h[p?"floating"===q?"reference":
"floating":q];b=wa(await f.getClippingRect({element:(null!=(c=await (null==f.isElement?void 0:f.isElement(b)))?c:1)?b:b.contextElement||await (null==f.getDocumentElement?void 0:f.getDocumentElement(h.floating)),boundary:k,rootBoundary:m,strategy:l}));var r="floating"===q?D({},g.floating,{x:d,y:e}):g.reference;let n=await (null==f.getOffsetParent?void 0:f.getOffsetParent(h.floating));c=await (null==f.isElement?void 0:f.isElement(n))?await (null==f.getScale?void 0:f.getScale(n))||{x:1,y:1}:{x:1,y:1};
r=wa(f.convertOffsetParentRelativeRectToViewportRelativeRect?await f.convertOffsetParentRelativeRectToViewportRelativeRect({rect:r,offsetParent:n,strategy:l}):r);return{top:(b.top-r.top+a.top)/c.y,bottom:(r.bottom-b.bottom+a.bottom)/c.y,left:(b.left-r.left+a.left)/c.x,right:(r.right-b.right+a.right)/c.x}}function T(a){return bb(a)?(a.nodeName||"").toLowerCase():"#document"}function E(a){var b;return(null==a?void 0:null==(b=a.ownerDocument)?void 0:b.defaultView)||window}function L(a){var b;return null==
(b=(bb(a)?a.ownerDocument:a.document)||window.document)?void 0:b.documentElement}function bb(a){return a instanceof Node||a instanceof E(a).Node}function M(a){return a instanceof Element||a instanceof E(a).Element}function J(a){return a instanceof HTMLElement||a instanceof E(a).HTMLElement}function cb(a){return"undefined"===typeof ShadowRoot?!1:a instanceof ShadowRoot||a instanceof E(a).ShadowRoot}function ua(a){let {overflow:b,overflowX:c,overflowY:d,display:e}=G(a);return/auto|scroll|overlay|hidden|clip/.test(b+
d+c)&&!["inline","contents"].includes(e)}function Ja(a){let b=Ka(),c=G(a);return"none"!==c.transform||"none"!==c.perspective||(c.containerType?"normal"!==c.containerType:!1)||!b&&(c.backdropFilter?"none"!==c.backdropFilter:!1)||!b&&(c.filter?"none"!==c.filter:!1)||["transform","perspective","filter"].some(d=>(c.willChange||"").includes(d))||["paint","layout","strict","content"].some(d=>(c.contain||"").includes(d))}function Ka(){return"undefined"!==typeof CSS&&CSS.supports?CSS.supports("-webkit-backdrop-filter",
"none"):!1}function xa(a){return["html","body","#document"].includes(T(a))}function G(a){return E(a).getComputedStyle(a)}function ya(a){return M(a)?{scrollLeft:a.scrollLeft,scrollTop:a.scrollTop}:{scrollLeft:a.pageXOffset,scrollTop:a.pageYOffset}}function ja(a){if("html"===T(a))return a;a=a.assignedSlot||a.parentNode||cb(a)&&a.host||L(a);return cb(a)?a.host:a}function db(a){let b=ja(a);return xa(b)?a.ownerDocument?a.ownerDocument.body:a.body:J(b)&&ua(b)?b:db(b)}function za(a,b){var c;void 0===b&&
(b=[]);let d=db(a);a=d===(null==(c=a.ownerDocument)?void 0:c.body);c=E(d);return a?b.concat(c,c.visualViewport||[],ua(d)?d:[]):b.concat(d,za(d))}function eb(a){var b=G(a);let c=parseFloat(b.width)||0;b=parseFloat(b.height)||0;var d=J(a);let e=d?a.offsetWidth:c;a=d?a.offsetHeight:b;if(d=Aa(c)!==e||Aa(b)!==a)c=e,b=a;return{width:c,height:b,$:d}}function La(a){return M(a)?a:a.contextElement}function ka(a){var b=La(a);if(!J(b))return{x:1,y:1};a=b.getBoundingClientRect();let {width:c,height:d,$:e}=eb(b);
b=(e?Aa(a.width):a.width)/c;a=(e?Aa(a.height):a.height)/d;b&&Number.isFinite(b)||(b=1);a&&Number.isFinite(a)||(a=1);return{x:b,y:a}}function fb(a){a=E(a);return Ka()&&a.visualViewport?{x:a.visualViewport.offsetLeft,y:a.visualViewport.offsetTop}:Mb}function Z(a,b,c,d){void 0===b&&(b=!1);void 0===c&&(c=!1);var e=a.getBoundingClientRect(),f=La(a),g={x:1,y:1};b&&(d?M(d)&&(g=ka(d)):g=ka(a));a=c;void 0===a&&(a=!1);a=!d||a&&d!==E(f)?!1:a;b=a?fb(f):{x:0,y:0};a=(e.left+b.x)/g.x;b=(e.top+b.y)/g.y;c=e.width/
g.x;e=e.height/g.y;if(f){f=E(f);g=d&&M(d)?E(d):d;let l=f.frameElement;for(;l&&d&&g!==f;){let k=ka(l);var h=l.getBoundingClientRect();let m=G(l),q=h.left+(l.clientLeft+parseFloat(m.paddingLeft))*k.x;h=h.top+(l.clientTop+parseFloat(m.paddingTop))*k.y;a*=k.x;b*=k.y;c*=k.x;e*=k.y;a+=q;b+=h;l=E(l).frameElement}}return wa({width:c,height:e,x:a,y:b})}function gb(a){return Z(L(a)).left+ya(a).scrollLeft}function hb(a,b,c){if("viewport"===b){b=E(a);var d=L(a);b=b.visualViewport;a=d.clientWidth;d=d.clientHeight;
var e=0,f=0;if(b){a=b.width;d=b.height;let g=Ka();if(!g||g&&"fixed"===c)e=b.offsetLeft,f=b.offsetTop}c={width:a,height:d,x:e,y:f}}else"document"===b?(f=L(a),c=L(f),e=ya(f),b=f.ownerDocument.body,a=N(c.scrollWidth,c.clientWidth,b.scrollWidth,b.clientWidth),d=N(c.scrollHeight,c.clientHeight,b.scrollHeight,b.clientHeight),f=-e.scrollLeft+gb(f),e=-e.scrollTop,"rtl"===G(b).direction&&(f+=N(c.clientWidth,b.clientWidth)-a),c={width:a,height:d,x:f,y:e}):M(b)?(a=Z(b,!0,"fixed"===c),c=a.top+b.clientTop,a=a.left+
b.clientLeft,d=J(b)?ka(b):{x:1,y:1},c={width:b.clientWidth*d.x,height:b.clientHeight*d.y,x:a*d.x,y:c*d.y}):(c=fb(a),c=D({},b,{x:b.x-c.x,y:b.y-c.y}));return wa(c)}function ib(a,b){a=ja(a);return a===b||!M(a)||xa(a)?!1:"fixed"===G(a).position||ib(a,b)}function Nb(a,b){var c=b.get(a);if(c)return c;c=za(a).filter(g=>M(g)&&"body"!==T(g));let d=null,e="fixed"===G(a).position,f=e?ja(a):a;for(;M(f)&&!xa(f);){let g=G(f),h=Ja(f);h||"fixed"!==g.position||(d=null);(e?!h&&!d:!h&&"static"===g.position&&d&&["absolute",
"fixed"].includes(d.position)||ua(f)&&!h&&ib(a,f))?c=c.filter(l=>l!==f):d=g;f=ja(f)}b.set(a,c);return c}function jb(a,b){return J(a)&&"fixed"!==G(a).position?b?b(a):a.offsetParent:null}function kb(a,b){let c=E(a);if(!J(a))return c;let d=jb(a,b);for(;d&&["table","td","th"].includes(T(d))&&"static"===G(d).position;)d=jb(d,b);if(d&&("html"===T(d)||"body"===T(d)&&"static"===G(d).position&&!Ja(d)))return c;if(!(b=d))a:{for(a=ja(a);J(a)&&!xa(a);)if(Ja(a)){b=a;break a}else a=ja(a);b=null}return b||c}function Ob(a,
b){function c(){clearTimeout(f);e&&e.disconnect();e=null}function d(h,l){function k(x){x=x[0].intersectionRatio;if(x!==l){if(!t)return d();x?d(!1,x):f=setTimeout(()=>{d(!1,1E-7)},100)}t=!1}void 0===h&&(h=!1);void 0===l&&(l=1);c();let {left:m,top:q,width:p,height:u}=a.getBoundingClientRect();h||b();if(p&&u){h=Ba(q);var r=Ba(g.clientWidth-(m+p)),n=Ba(g.clientHeight-(q+u)),v=Ba(m);h={rootMargin:-h+"px "+-r+"px "+-n+"px "+-v+"px",threshold:N(0,U(1,l))||1};var t=!0;try{e=new IntersectionObserver(k,D({},
h,{root:g.ownerDocument}))}catch(x){e=new IntersectionObserver(k,h)}e.observe(a)}}let e=null,f,g=L(a);d(!0);return c}function Pb(a,b,c,d){function e(){let t=Z(a);!v||t.x===v.x&&t.y===v.y&&t.width===v.width&&t.height===v.height||c();v=t;n=requestAnimationFrame(e)}void 0===d&&(d={});let {ancestorScroll:f=!0,ancestorResize:g=!0,elementResize:h="function"===typeof ResizeObserver,layoutShift:l="function"===typeof IntersectionObserver,animationFrame:k=!1}=d,m=La(a),q=f||g?[...(m?za(m):[]),...za(b)]:[];
q.forEach(t=>{f&&t.addEventListener("scroll",c,{passive:!0});g&&t.addEventListener("resize",c)});let p=m&&l?Ob(m,c):null,u=-1,r=null;h&&(r=new ResizeObserver(t=>{[t]=t;t&&t.target===m&&r&&(r.unobserve(b),cancelAnimationFrame(u),u=requestAnimationFrame(()=>{r&&r.observe(b)}));c()}),m&&!k&&r.observe(m),r.observe(b));let n,v=k?Z(a):null;k&&e();c();return()=>{q.forEach(t=>{f&&t.removeEventListener("scroll",c);g&&t.removeEventListener("resize",c)});p&&p();r&&r.disconnect();r=null;k&&cancelAnimationFrame(n)}}
function Qb(a){a.cleanup&&a.cleanup();let b=a._getResolvedAttachToOptions(),c=b.element,d=Rb(b,a),e=void 0===b||null===b?!0:!b.element||!b.on;e&&(c=document.body,a.shepherdElementComponent.getElement().classList.add("shepherd-centered"));a.cleanup=Pb(c,a.el,()=>{a.el?Sb(c,a,d,e):a.cleanup()});a.target=b.element;return d}function Sb(a,b,c,d){return Tb(a,b.el,c).then(Ub(b,d)).then(e=>new Promise(f=>{setTimeout(()=>f(e),300)})).then(e=>{e&&e.el&&e.el.focus({preventScroll:!0})})}function Ub(a,b){return({x:c,
y:d,placement:e,middlewareData:f})=>{if(!a.el)return a;b?Object.assign(a.el.style,{position:"fixed",left:"50%",top:"50%",transform:"translate(-50%, -50%)"}):Object.assign(a.el.style,{position:"absolute",left:`${c}px`,top:`${d}px`});a.el.dataset.popperPlacement=e;if((c=a.el.querySelector(".shepherd-arrow"))&&f.arrow){let {x:g,y:h}=f.arrow;Object.assign(c.style,{left:null!=g?`${g}px`:"",top:null!=h?`${h}px`:""})}return a}}function Rb(a,b){let c={strategy:"absolute",middleware:[]};var d=b.options.arrow&&
b.el?b.el.querySelector(".shepherd-arrow"):!1;void 0!==a&&null!==a&&a.element&&a.on&&(c.middleware.push(Vb(),Wb({limiter:Xb(),crossAxis:!0})),d&&c.middleware.push(Yb({element:d})),c.placement=a.on);return Ma(b.options.floatingUIOptions||{},c)}function F(){}function Zb(a,b){for(let c in b)a[c]=b[c];return a}function la(a){return a()}function Na(a){return"function"===typeof a}function O(a,b){return a!=a?b==b:a!==b||a&&"object"===typeof a||"function"===typeof a}function H(a){a.parentNode&&a.parentNode.removeChild(a)}
function lb(a){return document.createElementNS("http://www.w3.org/2000/svg",a)}function Ca(a,b,c,d){a.addEventListener(b,c,d);return()=>a.removeEventListener(b,c,d)}function z(a,b,c){null==c?a.removeAttribute(b):a.getAttribute(b)!==c&&a.setAttribute(b,c)}function mb(a,b){let c=Object.getOwnPropertyDescriptors(a.__proto__);for(let d in b)null==b[d]?a.removeAttribute(d):"style"===d?a.style.cssText=b[d]:"__value"===d?a.value=a[d]=b[d]:c[d]&&c[d].set&&-1===$b.indexOf(d)?a[d]=b[d]:z(a,d,b[d])}function ma(a,
b,c){a.classList[c?"add":"remove"](b)}function Da(){if(!P)throw Error("Function called outside component initialization");return P}function Oa(a){na.push(a)}function nb(){if(0===oa){var a=P;do{try{for(;oa<pa.length;){let c=pa[oa];oa++;P=c;var b=c.$$;if(null!==b.fragment){b.update();b.before_update.forEach(la);let d=b.dirty;b.dirty=[-1];b.fragment&&b.fragment.p(b.ctx,d);b.after_update.forEach(Oa)}}}catch(c){throw oa=pa.length=0,c;}P=null;for(oa=pa.length=0;qa.length;)qa.pop()();for(let c=0;c<na.length;c+=
1){let d=na[c];Pa.has(d)||(Pa.add(d),d())}na.length=0}while(pa.length);for(;ob.length;)ob.pop()();Qa=!1;Pa.clear();P=a}}function ac(a){let b=[],c=[];na.forEach(d=>-1===a.indexOf(d)?b.push(d):c.push(d));c.forEach(d=>d());na=b}function aa(){ba={r:0,c:[],p:ba}}function ca(){ba.r||ba.c.forEach(la);ba=ba.p}function y(a,b){a&&a.i&&(Ea.delete(a),a.i(b))}function B(a,b,c,d){a&&a.o?Ea.has(a)||(Ea.add(a),ba.c.push(()=>{Ea.delete(a);d&&(c&&a.d(1),d())}),a.o(b)):d&&d()}function da(a){a&&a.c()}function V(a,b,
c,d){let {fragment:e,after_update:f}=a.$$;e&&e.m(b,c);d||Oa(()=>{let g=a.$$.on_mount.map(la).filter(Na);a.$$.on_destroy?a.$$.on_destroy.push(...g):g.forEach(la);a.$$.on_mount=[]});f.forEach(Oa)}function W(a,b){a=a.$$;null!==a.fragment&&(ac(a.after_update),a.on_destroy.forEach(la),a.fragment&&a.fragment.d(b),a.on_destroy=a.fragment=null,a.ctx=[])}function Q(a,b,c,d,e,f,g,h=[-1]){let l=P;P=a;let k=a.$$={fragment:null,ctx:[],props:f,update:F,not_equal:e,bound:Object.create(null),on_mount:[],on_destroy:[],
on_disconnect:[],before_update:[],after_update:[],context:new Map(b.context||(l?l.$$.context:[])),callbacks:Object.create(null),dirty:h,skip_bound:!1,root:b.target||l.$$.root};g&&g(k.root);let m=!1;k.ctx=c?c(a,b.props||{},(q,p,...u)=>{u=u.length?u[0]:p;if(k.ctx&&e(k.ctx[q],k.ctx[q]=u)){if(!k.skip_bound&&k.bound[q])k.bound[q](u);m&&(-1===a.$$.dirty[0]&&(pa.push(a),Qa||(Qa=!0,bc.then(nb)),a.$$.dirty.fill(0)),a.$$.dirty[q/31|0]|=1<<q%31)}return p}):[];k.update();m=!0;k.before_update.forEach(la);k.fragment=
d?d(k.ctx):!1;b.target&&(b.hydrate?(c=Array.from(b.target.childNodes),k.fragment&&k.fragment.l(c),c.forEach(H)):k.fragment&&k.fragment.c(),b.intro&&y(a.$$.fragment),V(a,b.target,b.anchor,b.customElement),nb());P=l}function cc(a){let b,c,d,e,f;return{c(){b=document.createElement("button");z(b,"aria-label",c=a[3]?a[3]:null);z(b,"class",d=`${a[1]||""} shepherd-button ${a[4]?"shepherd-button-secondary":""}`);b.disabled=a[2];z(b,"tabindex","0")},m(g,h){g.insertBefore(b,h||null);b.innerHTML=a[5];e||(f=
Ca(b,"click",function(){Na(a[0])&&a[0].apply(this,arguments)}),e=!0)},p(g,[h]){a=g;h&32&&(b.innerHTML=a[5]);h&8&&c!==(c=a[3]?a[3]:null)&&z(b,"aria-label",c);h&18&&d!==(d=`${a[1]||""} shepherd-button ${a[4]?"shepherd-button-secondary":""}`)&&z(b,"class",d);h&4&&(b.disabled=a[2])},i:F,o:F,d(g){g&&H(b);e=!1;f()}}}function dc(a,b,c){function d(p){return X(p)?p.call(f):p}let {config:e,step:f}=b,g,h,l,k,m,q;a.$$set=p=>{"config"in p&&c(6,e=p.config);"step"in p&&c(7,f=p.step)};a.$$.update=()=>{a.$$.dirty&
192&&(c(0,g=e.action?e.action.bind(f.tour):null),c(1,h=e.classes),c(2,l=e.disabled?d(e.disabled):!1),c(3,k=e.label?d(e.label):null),c(4,m=e.secondary),c(5,q=e.text?d(e.text):null))};return[g,h,l,k,m,q,e,f]}function pb(a,b,c){a=a.slice();a[2]=b[c];return a}function qb(a){let b,c,d=a[1],e=[];for(let g=0;g<d.length;g+=1)e[g]=rb(pb(a,d,g));let f=g=>B(e[g],1,1,()=>{e[g]=null});return{c(){for(let g=0;g<e.length;g+=1)e[g].c();b=document.createTextNode("")},m(g,h){for(let l=0;l<e.length;l+=1)e[l]&&e[l].m(g,
h);g.insertBefore(b,h||null);c=!0},p(g,h){if(h&3){d=g[1];let l;for(l=0;l<d.length;l+=1){let k=pb(g,d,l);e[l]?(e[l].p(k,h),y(e[l],1)):(e[l]=rb(k),e[l].c(),y(e[l],1),e[l].m(b.parentNode,b))}aa();for(l=d.length;l<e.length;l+=1)f(l);ca()}},i(g){if(!c){for(g=0;g<d.length;g+=1)y(e[g]);c=!0}},o(g){e=e.filter(Boolean);for(g=0;g<e.length;g+=1)B(e[g]);c=!1},d(g){var h=e;for(let l=0;l<h.length;l+=1)h[l]&&h[l].d(g);g&&H(b)}}}function rb(a){let b,c;b=new ec({props:{config:a[2],step:a[0]}});return{c(){da(b.$$.fragment)},
m(d,e){V(b,d,e);c=!0},p(d,e){let f={};e&2&&(f.config=d[2]);e&1&&(f.step=d[0]);b.$set(f)},i(d){c||(y(b.$$.fragment,d),c=!0)},o(d){B(b.$$.fragment,d);c=!1},d(d){W(b,d)}}}function fc(a){let b,c,d=a[1]&&qb(a);return{c(){b=document.createElement("footer");d&&d.c();z(b,"class","shepherd-footer")},m(e,f){e.insertBefore(b,f||null);d&&d.m(b,null);c=!0},p(e,[f]){e[1]?d?(d.p(e,f),f&2&&y(d,1)):(d=qb(e),d.c(),y(d,1),d.m(b,null)):d&&(aa(),B(d,1,1,()=>{d=null}),ca())},i(e){c||(y(d),c=!0)},o(e){B(d);c=!1},d(e){e&&
H(b);d&&d.d()}}}function gc(a,b,c){let d,{step:e}=b;a.$$set=f=>{"step"in f&&c(0,e=f.step)};a.$$.update=()=>{a.$$.dirty&1&&c(1,d=e.options.buttons)};return[e,d]}function hc(a){let b,c,d,e,f;return{c(){b=document.createElement("button");c=document.createElement("span");c.textContent="\u00d7";z(c,"aria-hidden","true");z(b,"aria-label",d=a[0].label?a[0].label:"Close Tour");z(b,"class","shepherd-cancel-icon");z(b,"type","button")},m(g,h){g.insertBefore(b,h||null);b.appendChild(c);e||(f=Ca(b,"click",a[1]),
e=!0)},p(g,[h]){h&1&&d!==(d=g[0].label?g[0].label:"Close Tour")&&z(b,"aria-label",d)},i:F,o:F,d(g){g&&H(b);e=!1;f()}}}function ic(a,b,c){let {cancelIcon:d,step:e}=b;a.$$set=f=>{"cancelIcon"in f&&c(0,d=f.cancelIcon);"step"in f&&c(2,e=f.step)};return[d,f=>{f.preventDefault();e.cancel()},e]}function jc(a){let b;return{c(){b=document.createElement("h3");z(b,"id",a[1]);z(b,"class","shepherd-title")},m(c,d){c.insertBefore(b,d||null);a[3](b)},p(c,[d]){d&2&&z(b,"id",c[1])},i:F,o:F,d(c){c&&H(b);a[3](null)}}}
function kc(a,b,c){let {labelId:d,element:e,title:f}=b;Da().$$.after_update.push(()=>{X(f)&&c(2,f=f());c(0,e.innerHTML=f,e)});a.$$set=g=>{"labelId"in g&&c(1,d=g.labelId);"element"in g&&c(0,e=g.element);"title"in g&&c(2,f=g.title)};return[e,d,f,function(g){qa[g?"unshift":"push"](()=>{e=g;c(0,e)})}]}function sb(a){let b,c;b=new lc({props:{labelId:a[0],title:a[2]}});return{c(){da(b.$$.fragment)},m(d,e){V(b,d,e);c=!0},p(d,e){let f={};e&1&&(f.labelId=d[0]);e&4&&(f.title=d[2]);b.$set(f)},i(d){c||(y(b.$$.fragment,
d),c=!0)},o(d){B(b.$$.fragment,d);c=!1},d(d){W(b,d)}}}function tb(a){let b,c;b=new mc({props:{cancelIcon:a[3],step:a[1]}});return{c(){da(b.$$.fragment)},m(d,e){V(b,d,e);c=!0},p(d,e){let f={};e&8&&(f.cancelIcon=d[3]);e&2&&(f.step=d[1]);b.$set(f)},i(d){c||(y(b.$$.fragment,d),c=!0)},o(d){B(b.$$.fragment,d);c=!1},d(d){W(b,d)}}}function nc(a){let b,c,d,e=a[2]&&sb(a),f=a[3]&&a[3].enabled&&tb(a);return{c(){b=document.createElement("header");e&&e.c();c=document.createTextNode(" ");f&&f.c();z(b,"class","shepherd-header")},
m(g,h){g.insertBefore(b,h||null);e&&e.m(b,null);b.appendChild(c);f&&f.m(b,null);d=!0},p(g,[h]){g[2]?e?(e.p(g,h),h&4&&y(e,1)):(e=sb(g),e.c(),y(e,1),e.m(b,c)):e&&(aa(),B(e,1,1,()=>{e=null}),ca());g[3]&&g[3].enabled?f?(f.p(g,h),h&8&&y(f,1)):(f=tb(g),f.c(),y(f,1),f.m(b,null)):f&&(aa(),B(f,1,1,()=>{f=null}),ca())},i(g){d||(y(e),y(f),d=!0)},o(g){B(e);B(f);d=!1},d(g){g&&H(b);e&&e.d();f&&f.d()}}}function oc(a,b,c){let {labelId:d,step:e}=b,f,g;a.$$set=h=>{"labelId"in h&&c(0,d=h.labelId);"step"in h&&c(1,e=
h.step)};a.$$.update=()=>{a.$$.dirty&2&&(c(2,f=e.options.title),c(3,g=e.options.cancelIcon))};return[d,e,f,g]}function pc(a){let b;return{c(){b=document.createElement("div");z(b,"class","shepherd-text");z(b,"id",a[1])},m(c,d){c.insertBefore(b,d||null);a[3](b)},p(c,[d]){d&2&&z(b,"id",c[1])},i:F,o:F,d(c){c&&H(b);a[3](null)}}}function qc(a,b,c){let {descriptionId:d,element:e,step:f}=b;Da().$$.after_update.push(()=>{let {text:g}=f.options;X(g)&&(g=g.call(f));g instanceof HTMLElement?e.appendChild(g):
c(0,e.innerHTML=g,e)});a.$$set=g=>{"descriptionId"in g&&c(1,d=g.descriptionId);"element"in g&&c(0,e=g.element);"step"in g&&c(2,f=g.step)};return[e,d,f,function(g){qa[g?"unshift":"push"](()=>{e=g;c(0,e)})}]}function ub(a){let b,c;b=new rc({props:{labelId:a[1],step:a[2]}});return{c(){da(b.$$.fragment)},m(d,e){V(b,d,e);c=!0},p(d,e){let f={};e&2&&(f.labelId=d[1]);e&4&&(f.step=d[2]);b.$set(f)},i(d){c||(y(b.$$.fragment,d),c=!0)},o(d){B(b.$$.fragment,d);c=!1},d(d){W(b,d)}}}function vb(a){let b,c;b=new sc({props:{descriptionId:a[0],
step:a[2]}});return{c(){da(b.$$.fragment)},m(d,e){V(b,d,e);c=!0},p(d,e){let f={};e&1&&(f.descriptionId=d[0]);e&4&&(f.step=d[2]);b.$set(f)},i(d){c||(y(b.$$.fragment,d),c=!0)},o(d){B(b.$$.fragment,d);c=!1},d(d){W(b,d)}}}function wb(a){let b,c;b=new tc({props:{step:a[2]}});return{c(){da(b.$$.fragment)},m(d,e){V(b,d,e);c=!0},p(d,e){let f={};e&4&&(f.step=d[2]);b.$set(f)},i(d){c||(y(b.$$.fragment,d),c=!0)},o(d){B(b.$$.fragment,d);c=!1},d(d){W(b,d)}}}function uc(a){let b,c=void 0!==a[2].options.title||a[2].options.cancelIcon&&
a[2].options.cancelIcon.enabled,d,e=void 0!==a[2].options.text,f,g=Array.isArray(a[2].options.buttons)&&a[2].options.buttons.length,h,l=c&&ub(a),k=e&&vb(a),m=g&&wb(a);return{c(){b=document.createElement("div");l&&l.c();d=document.createTextNode(" ");k&&k.c();f=document.createTextNode(" ");m&&m.c();z(b,"class","shepherd-content")},m(q,p){q.insertBefore(b,p||null);l&&l.m(b,null);b.appendChild(d);k&&k.m(b,null);b.appendChild(f);m&&m.m(b,null);h=!0},p(q,[p]){p&4&&(c=void 0!==q[2].options.title||q[2].options.cancelIcon&&
q[2].options.cancelIcon.enabled);c?l?(l.p(q,p),p&4&&y(l,1)):(l=ub(q),l.c(),y(l,1),l.m(b,d)):l&&(aa(),B(l,1,1,()=>{l=null}),ca());p&4&&(e=void 0!==q[2].options.text);e?k?(k.p(q,p),p&4&&y(k,1)):(k=vb(q),k.c(),y(k,1),k.m(b,f)):k&&(aa(),B(k,1,1,()=>{k=null}),ca());p&4&&(g=Array.isArray(q[2].options.buttons)&&q[2].options.buttons.length);g?m?(m.p(q,p),p&4&&y(m,1)):(m=wb(q),m.c(),y(m,1),m.m(b,null)):m&&(aa(),B(m,1,1,()=>{m=null}),ca())},i(q){h||(y(l),y(k),y(m),h=!0)},o(q){B(l);B(k);B(m);h=!1},d(q){q&&H(b);
l&&l.d();k&&k.d();m&&m.d()}}}function vc(a,b,c){let {descriptionId:d,labelId:e,step:f}=b;a.$$set=g=>{"descriptionId"in g&&c(0,d=g.descriptionId);"labelId"in g&&c(1,e=g.labelId);"step"in g&&c(2,f=g.step)};return[d,e,f]}function xb(a){let b;return{c(){b=document.createElement("div");z(b,"class","shepherd-arrow");z(b,"data-popper-arrow","")},m(c,d){c.insertBefore(b,d||null)},d(c){c&&H(b)}}}function wc(a){let b,c,d,e,f,g,h,l,k=a[4].options.arrow&&a[4].options.attachTo&&a[4].options.attachTo.element&&
a[4].options.attachTo.on&&xb();d=new xc({props:{descriptionId:a[2],labelId:a[3],step:a[4]}});let m=[{"aria-describedby":e=void 0!==a[4].options.text?a[2]:null},{"aria-labelledby":f=a[4].options.title?a[3]:null},a[1],{role:"dialog"},{tabindex:"0"}],q={};for(let p=0;p<m.length;p+=1)q=Zb(q,m[p]);return{c(){b=document.createElement("div");k&&k.c();c=document.createTextNode(" ");da(d.$$.fragment);mb(b,q);ma(b,"shepherd-has-cancel-icon",a[5]);ma(b,"shepherd-has-title",a[6]);ma(b,"shepherd-element",!0)},
m(p,u){p.insertBefore(b,u||null);k&&k.m(b,null);b.appendChild(c);V(d,b,null);a[13](b);g=!0;h||(l=Ca(b,"keydown",a[7]),h=!0)},p(p,[u]){p[4].options.arrow&&p[4].options.attachTo&&p[4].options.attachTo.element&&p[4].options.attachTo.on?k||(k=xb(),k.c(),k.m(b,c)):k&&(k.d(1),k=null);var r={};u&4&&(r.descriptionId=p[2]);u&8&&(r.labelId=p[3]);u&16&&(r.step=p[4]);d.$set(r);r=b;u=[(!g||u&20&&e!==(e=void 0!==p[4].options.text?p[2]:null))&&{"aria-describedby":e},(!g||u&24&&f!==(f=p[4].options.title?p[3]:null))&&
{"aria-labelledby":f},u&2&&p[1],{role:"dialog"},{tabindex:"0"}];let n={},v={},t={$$scope:1},x=m.length;for(;x--;){let A=m[x],w=u[x];if(w){for(let C in A)C in w||(v[C]=1);for(let C in w)t[C]||(n[C]=w[C],t[C]=1);m[x]=w}else for(let C in A)t[C]=1}for(let A in v)A in n||(n[A]=void 0);mb(r,q=n);ma(b,"shepherd-has-cancel-icon",p[5]);ma(b,"shepherd-has-title",p[6]);ma(b,"shepherd-element",!0)},i(p){g||(y(d.$$.fragment,p),g=!0)},o(p){B(d.$$.fragment,p);g=!1},d(p){p&&H(b);k&&k.d();W(d);a[13](null);h=!1;l()}}}
function yb(a){return a.split(" ").filter(b=>!!b.length)}function yc(a,b,c){let {classPrefix:d,element:e,descriptionId:f,firstFocusableElement:g,focusableElements:h,labelId:l,lastFocusableElement:k,step:m,dataStepId:q}=b,p,u,r;Da().$$.on_mount.push(()=>{c(1,q={[`data-${d}shepherd-step-id`]:m.id});c(9,h=e.querySelectorAll('a[href], area[href], input:not([disabled]), select:not([disabled]), textarea:not([disabled]), button:not([disabled]), [tabindex="0"]'));c(8,g=h[0]);c(10,k=h[h.length-1])});Da().$$.after_update.push(()=>
{if(r!==m.options.classes){var n=r;sa(n)&&(n=yb(n),n.length&&e.classList.remove(...n));n=r=m.options.classes;sa(n)&&(n=yb(n),n.length&&e.classList.add(...n))}});a.$$set=n=>{"classPrefix"in n&&c(11,d=n.classPrefix);"element"in n&&c(0,e=n.element);"descriptionId"in n&&c(2,f=n.descriptionId);"firstFocusableElement"in n&&c(8,g=n.firstFocusableElement);"focusableElements"in n&&c(9,h=n.focusableElements);"labelId"in n&&c(3,l=n.labelId);"lastFocusableElement"in n&&c(10,k=n.lastFocusableElement);"step"in
n&&c(4,m=n.step);"dataStepId"in n&&c(1,q=n.dataStepId)};a.$$.update=()=>{a.$$.dirty&16&&(c(5,p=m.options&&m.options.cancelIcon&&m.options.cancelIcon.enabled),c(6,u=m.options&&m.options.title))};return[e,q,f,l,m,p,u,n=>{const {tour:v}=m;switch(n.keyCode){case 9:if(0===h.length){n.preventDefault();break}if(n.shiftKey){if(document.activeElement===g||document.activeElement.classList.contains("shepherd-element"))n.preventDefault(),k.focus()}else document.activeElement===k&&(n.preventDefault(),g.focus());
break;case 27:v.options.exitOnEsc&&(n.stopPropagation(),m.cancel());break;case 37:v.options.keyboardNavigation&&(n.stopPropagation(),v.back());break;case 39:v.options.keyboardNavigation&&(n.stopPropagation(),v.next())}},g,h,k,d,()=>e,function(n){qa[n?"unshift":"push"](()=>{e=n;c(0,e)})}]}function zc(a){a&&({steps:a}=a,a.forEach(b=>{b.options&&!1===b.options.canClickTarget&&b.options.attachTo&&b.target instanceof HTMLElement&&b.target.classList.remove("shepherd-target-click-disabled")}))}function Ac({width:a,
height:b,x:c=0,y:d=0,r:e=0}){let {innerWidth:f,innerHeight:g}=window,{topLeft:h=0,topRight:l=0,bottomRight:k=0,bottomLeft:m=0}="number"===typeof e?{topLeft:e,topRight:e,bottomRight:e,bottomLeft:e}:e;return`M${f},${g}\
H0\
V0\
H${f}\
V${g}\
Z\
M${c+h},${d}\
a${h},${h},0,0,0-${h},${h}\
V${b+d-m}\
a${m},${m},0,0,0,${m},${m}\
H${a+c-k}\
a${k},${k},0,0,0,${k}-${k}\
V${d+l}\
a${l},${l},0,0,0-${l}-${l}\
Z`}function Bc(a){let b,c,d,e,f;return{c(){b=lb("svg");c=lb("path");z(c,"d",a[2]);z(b,"class",d=`${a[1]?"shepherd-modal-is-visible":""} shepherd-modal-overlay-container`)},m(g,h){g.insertBefore(b,h||null);b.appendChild(c);a[11](b);e||(f=Ca(b,"touchmove",a[3]),e=!0)},p(g,[h]){h&4&&z(c,"d",g[2]);h&2&&d!==(d=`${g[1]?"shepherd-modal-is-visible":""} shepherd-modal-overlay-container`)&&z(b,"class",d)},i:F,o:F,d(g){g&&H(b);a[11](null);e=!1;f()}}}function zb(a){if(!a)return null;let b=a instanceof HTMLElement&&
window.getComputedStyle(a).overflowY;return"hidden"!==b&&"visible"!==b&&a.scrollHeight>=a.clientHeight?a:zb(a.parentElement)}function Cc(a,b,c){function d(){c(4,m={width:0,height:0,x:0,y:0,r:0})}function e(){c(1,q=!1);h()}function f(n=0,v=0,t,x){if(x){var A=x.getBoundingClientRect();let C=A.y||A.top;A=A.bottom||C+A.height;if(t){var w=t.getBoundingClientRect();t=w.y||w.top;w=w.bottom||t+w.height;C=Math.max(C,t);A=Math.min(A,w)}let {y:Fa,height:I}={y:C,height:Math.max(A-C,0)},{x:R,width:Ra,left:Dc}=
x.getBoundingClientRect();c(4,m={width:Ra+2*n,height:I+2*n,x:(R||Dc)-n,y:Fa-n,r:v})}else d()}function g(){c(1,q=!0)}function h(){p&&(cancelAnimationFrame(p),p=void 0);window.removeEventListener("touchmove",r,{passive:!1})}function l(n){let {modalOverlayOpeningPadding:v,modalOverlayOpeningRadius:t}=n.options,x=zb(n.target),A=()=>{p=void 0;f(v,t,x,n.target);p=requestAnimationFrame(A)};A();window.addEventListener("touchmove",r,{passive:!1})}let {element:k,openingProperties:m}=b;Ga();let q=!1,p=void 0,
u;d();let r=n=>{n.preventDefault()};a.$$set=n=>{"element"in n&&c(0,k=n.element);"openingProperties"in n&&c(4,m=n.openingProperties)};a.$$.update=()=>{a.$$.dirty&16&&c(2,u=Ac(m))};return[k,q,u,n=>{n.stopPropagation()},m,()=>k,d,e,f,function(n){h();n.tour.options.useModalOverlay?(l(n),g()):e()},g,function(n){qa[n?"unshift":"push"](()=>{k=n;c(0,k)})}]}var Db=function(a){var b;if(b=!!a&&"object"===typeof a)b=Object.prototype.toString.call(a),b=!("[object RegExp]"===b||"[object Date]"===b||a.$$typeof===
Ec);return b},Ec="function"===typeof Symbol&&Symbol.for?Symbol.for("react.element"):60103;fa.all=function(a,b){if(!Array.isArray(a))throw Error("first argument should be an array");return a.reduce(function(c,d){return fa(c,d,b)},{})};var Ma=fa;class Sa{on(a,b,c,d=!1){void 0===this.bindings&&(this.bindings={});void 0===this.bindings[a]&&(this.bindings[a]=[]);this.bindings[a].push({handler:b,ctx:c,once:d});return this}once(a,b,c){return this.on(a,b,c,!0)}off(a,b){if(void 0===this.bindings||void 0===
this.bindings[a])return this;void 0===b?delete this.bindings[a]:this.bindings[a].forEach((c,d)=>{c.handler===b&&this.bindings[a].splice(d,1)});return this}trigger(a,...b){void 0!==this.bindings&&this.bindings[a]&&this.bindings[a].forEach((c,d)=>{let {ctx:e,handler:f,once:g}=c;f.apply(e||this,b);g&&this.bindings[a].splice(d,1)});return this}}let U=Math.min,N=Math.max,Aa=Math.round,Ba=Math.floor,Lb={left:"right",right:"left",bottom:"top",top:"bottom"},Ib={start:"end",end:"start"},Fc="mainAxis crossAxis fallbackPlacements fallbackStrategy fallbackAxisSideDirection flipAlignment".split(" "),
Gc=["mainAxis","crossAxis","limiter"],Hc=async(a,b,c)=>{const {placement:d="bottom",strategy:e="absolute",middleware:f=[],platform:g}=c;c=f.filter(Boolean);const h=await (null==g.isRTL?void 0:g.isRTL(b));let l=await g.getElementRects({reference:a,floating:b,strategy:e}),{x:k,y:m}=$a(l,d,h),q=d,p={},u=0;for(let r=0;r<c.length;r++){const {name:n,fn:v}=c[r],{x:t,y:x,data:A,reset:w}=await v({x:k,y:m,initialPlacement:d,placement:q,strategy:e,middlewareData:p,rects:l,platform:g,elements:{reference:a,floating:b}});
k=null!=t?t:k;m=null!=x?x:m;p=D({},p,{[n]:D({},p[n],A)});w&&50>=u&&(u++,"object"===typeof w&&(w.placement&&(q=w.placement),w.rects&&(l=!0===w.rects?await g.getElementRects({reference:a,floating:b,strategy:e}):w.rects),{x:k,y:m}=$a(l,q,h)),r=-1)}return{x:k,y:m,placement:q,strategy:e,middlewareData:p}},Yb=a=>({name:"arrow",options:a,async fn(b){const {x:c,y:d,placement:e,rects:f,platform:g,elements:h}=b,{element:l,padding:k=0}=ha(a,b)||{};if(null==l)return{};var m=Za(k);b={x:c,y:d};const q=ta(ia(e));
var p=Ha(q);const u=await g.getDimensions(l);var r="y"===q,n=r?"top":"left",v=r?"bottom":"right",t=r?"clientHeight":"clientWidth",x=f.reference[p]+f.reference[q]-b[q]-f.floating[p];const A=b[q]-f.reference[q],w=await (null==g.getOffsetParent?void 0:g.getOffsetParent(l));(r=w?w[t]:0)&&await (null==g.isElement?void 0:g.isElement(w))||(r=h.floating[t]||f.floating[p]);t=x/2-A/2;x=r/2-u[p]/2-1;n=U(m[n],x);m=U(m[v],x);v=r-u[p]-m;r=r/2-u[p]/2+t;t=N(n,U(r,v));p=null!=e.split("-")[1]&&r!=t&&0>f.reference[p]/
2-(r<n?n:m)-u[p]/2?r<n?n-r:v-r:0;return{[q]:b[q]-p,data:{[q]:t,centerOffset:r-t+p}}}}),Vb=function(a){void 0===a&&(a={});return{name:"flip",options:a,async fn(b){var c;const {placement:d,middlewareData:e,rects:f,initialPlacement:g,platform:h,elements:l}=b;var k=ha(a,b);const {mainAxis:m=!0,crossAxis:q=!0,fallbackPlacements:p,fallbackStrategy:u="bestFit",fallbackAxisSideDirection:r="none",flipAlignment:n=!0}=k;var v=Ya(k,Fc);k=Y(d);var t=Y(g)===g;const x=await (null==h.isRTL?void 0:h.isRTL(l.floating));
t=p||(t||!n?[va(g)]:Hb(g));p||"none"===r||t.push(...Kb(g,n,r,x));t=[g,...t];v=await ab(b,v);const A=[];b=(null==(c=e.flip)?void 0:c.overflows)||[];m&&A.push(v[k]);q&&(c=Gb(d,f,x),A.push(v[c[0]],v[c[1]]));b=[...b,{placement:d,overflows:A}];if(!A.every(I=>0>=I)){var w,C;c=((null==(w=e.flip)?void 0:w.index)||0)+1;if(w=t[c])return{data:{index:c,overflows:b},reset:{placement:w}};w=null==(C=b.filter(I=>0>=I.overflows[0]).sort((I,R)=>I.overflows[1]-R.overflows[1])[0])?void 0:C.placement;if(!w)switch(u){case "bestFit":var Fa;
(C=null==(Fa=b.map(I=>[I.placement,I.overflows.filter(R=>0<R).reduce((R,Ra)=>R+Ra,0)]).sort((I,R)=>I[1]-R[1])[0])?void 0:Fa[0])&&(w=C);break;case "initialPlacement":w=g}if(d!==w)return{reset:{placement:w}}}return{}}}},Wb=function(a){void 0===a&&(a={});return{name:"shift",options:a,async fn(b){const {x:c,y:d,placement:e}=b;var f=ha(a,b);const {mainAxis:g=!0,crossAxis:h=!1,limiter:l={fn:u=>{let {x:r,y:n}=u;return{x:r,y:n}}}}=f;var k=Ya(f,Gc);f={x:c,y:d};k=await ab(b,k);const m=ia(Y(e)),q=ta(m);let p=
f[q];f=f[m];g&&(p=N(p+k["y"===q?"top":"left"],U(p,p-k["y"===q?"bottom":"right"])));h&&(f=N(f+k["y"===m?"top":"left"],U(f,f-k["y"===m?"bottom":"right"])));b=l.fn(D({},b,{[q]:p,[m]:f}));return D({},b,{data:{x:b.x-c,y:b.y-d}})}}},Xb=function(a){void 0===a&&(a={});return{options:a,fn(b){const {x:c,y:d,placement:e,rects:f,middlewareData:g}=b,{offset:h=0,mainAxis:l=!0,crossAxis:k=!0}=ha(a,b);var m={x:c,y:d};const q=ia(e),p=ta(q);let u=m[p];m=m[q];b=ha(h,b);b="number"===typeof b?{mainAxis:b,crossAxis:0}:
D({mainAxis:0,crossAxis:0},b);if(l){var r="y"===p?"height":"width",n=f.reference[p]-f.floating[r]+b.mainAxis;r=f.reference[p]+f.reference[r]-b.mainAxis;u<n?u=n:u>r&&(u=r)}if(k){var v,t;r="y"===p?"width":"height";const x=["top","left"].includes(Y(e));n=f.reference[q]-f.floating[r]+(x?(null==(v=g.offset)?void 0:v[q])||0:0)+(x?0:b.crossAxis);v=f.reference[q]+f.reference[r]+(x?0:(null==(t=g.offset)?void 0:t[q])||0)-(x?b.crossAxis:0);m<n?m=n:m>v&&(m=v)}return{[p]:u,[q]:m}}}},Mb={x:0,y:0},Ic={convertOffsetParentRelativeRectToViewportRelativeRect:function(a){let {rect:b,
offsetParent:c,strategy:d}=a;var e=J(c);let f=L(c);if(c===f)return b;a={scrollLeft:0,scrollTop:0};let g={x:1,y:1};var h=0,l=0;if(e||!e&&"fixed"!==d){if("body"!==T(c)||ua(f))a=ya(c);J(c)&&(e=Z(c),g=ka(c),h=e.x+c.clientLeft,l=e.y+c.clientTop)}return{width:b.width*g.x,height:b.height*g.y,x:b.x*g.x-a.scrollLeft*g.x+h,y:b.y*g.y-a.scrollTop*g.y+l}},getDocumentElement:L,getClippingRect:function(a){let {element:b,boundary:c,rootBoundary:d,strategy:e}=a;a=[...("clippingAncestors"===c?Nb(b,this._c):[].concat(c)),
d];a=a.reduce((f,g)=>{g=hb(b,g,e);f.top=N(g.top,f.top);f.right=U(g.right,f.right);f.bottom=U(g.bottom,f.bottom);f.left=N(g.left,f.left);return f},hb(b,a[0],e));return{width:a.right-a.left,height:a.bottom-a.top,x:a.left,y:a.top}},getOffsetParent:kb,getElementRects:async function(a){let {reference:b,floating:c,strategy:d}=a;a=this.getDimensions;var e=await (this.getOffsetParent||kb)(c),f=J(e);let g=L(e),h="fixed"===d,l=Z(b,!0,h,e),k={scrollLeft:0,scrollTop:0};var m=0,q=0;if(f||!f&&!h){if("body"!==T(e)||
ua(g))k=ya(e);f?(f=Z(e,!0,h,e),m=f.x+e.clientLeft,q=f.y+e.clientTop):g&&(m=gb(g))}return{reference:{x:l.left+k.scrollLeft-m,y:l.top+k.scrollTop-q,width:l.width,height:l.height},floating:D({x:0,y:0},await a(c))}},getClientRects:function(a){return Array.from(a.getClientRects())},getDimensions:function(a){return eb(a)},getScale:ka,isElement:M,isRTL:function(a){return"rtl"===G(a).direction}},Tb=(a,b,c)=>{var d=new Map;c=D({platform:Ic},c);d=D({},c.platform,{_c:d});return Hc(a,b,D({},c,{platform:d}))},
$b=["width","height"],P,pa=[],qa=[],na=[],ob=[],bc=Promise.resolve(),Qa=!1,Pa=new Set,oa=0,Ea=new Set,ba;class S{$destroy(){W(this,1);this.$destroy=F}$on(a,b){if(!Na(b))return F;let c=this.$$.callbacks[a]||(this.$$.callbacks[a]=[]);c.push(b);return()=>{let d=c.indexOf(b);-1!==d&&c.splice(d,1)}}$set(a){this.$$set&&0!==Object.keys(a).length&&(this.$$.skip_bound=!0,this.$$set(a),this.$$.skip_bound=!1)}}class ec extends S{constructor(a){super();Q(this,a,dc,cc,O,{config:6,step:7})}}class tc extends S{constructor(a){super();
Q(this,a,gc,fc,O,{step:0})}}class mc extends S{constructor(a){super();Q(this,a,ic,hc,O,{cancelIcon:0,step:2})}}class lc extends S{constructor(a){super();Q(this,a,kc,jc,O,{labelId:1,element:0,title:2})}}class rc extends S{constructor(a){super();Q(this,a,oc,nc,O,{labelId:0,step:1})}}class sc extends S{constructor(a){super();Q(this,a,qc,pc,O,{descriptionId:1,element:0,step:2})}}class xc extends S{constructor(a){super();Q(this,a,vc,uc,O,{descriptionId:0,labelId:1,step:2})}}class Jc extends S{constructor(a){super();
Q(this,a,yc,wc,O,{classPrefix:11,element:0,descriptionId:2,firstFocusableElement:8,focusableElements:9,labelId:3,lastFocusableElement:10,step:4,dataStepId:1,getElement:12})}get getElement(){return this.$$.ctx[12]}}class Ta extends Sa{constructor(a,b={}){super(a,b);this.tour=a;this.classPrefix=this.tour.options?Xa(this.tour.options.classPrefix):"";this.styles=a.styles;this._resolvedAttachTo=null;Wa(this);this._setOptions(b);return this}cancel(){this.tour.cancel();this.trigger("cancel")}complete(){this.tour.complete();
this.trigger("complete")}destroy(){this.cleanup&&this.cleanup();this.cleanup=null;this.el instanceof HTMLElement&&(this.el.remove(),this.el=null);this._updateStepTargetOnHide();this.trigger("destroy")}getTour(){return this.tour}hide(){this.tour.modal.hide();this.trigger("before-hide");this.el&&(this.el.hidden=!0);this._updateStepTargetOnHide();this.trigger("hide")}_resolveAttachToOptions(){let a=this.options.attachTo||{},b=Object.assign({},a);X(b.element)&&(b.element=b.element.call(this));if(sa(b.element)){try{b.element=
document.querySelector(b.element)}catch(c){}b.element||console.error(`The element for this Shepherd step was not found ${a.element}`)}return this._resolvedAttachTo=b}_getResolvedAttachToOptions(){return null===this._resolvedAttachTo?this._resolveAttachToOptions():this._resolvedAttachTo}isOpen(){return!(!this.el||this.el.hidden)}show(){return X(this.options.beforeShowPromise)?Promise.resolve(this.options.beforeShowPromise()).then(()=>this._show()):Promise.resolve(this._show())}updateStepOptions(a){Object.assign(this.options,
a);this.shepherdElementComponent&&this.shepherdElementComponent.$set({step:this})}getElement(){return this.el}getTarget(){return this.target}_createTooltipContent(){this.shepherdElementComponent=new Jc({target:this.tour.options.stepsContainer||document.body,props:{classPrefix:this.classPrefix,descriptionId:`${this.id}-description`,labelId:`${this.id}-label`,step:this,styles:this.styles}});return this.shepherdElementComponent.getElement()}_scrollTo(a){let {element:b}=this._getResolvedAttachToOptions();
X(this.options.scrollToHandler)?this.options.scrollToHandler(b):b instanceof Element&&"function"===typeof b.scrollIntoView&&b.scrollIntoView(a)}_getClassOptions(a){var b=this.tour&&this.tour.options&&this.tour.options.defaultStepOptions;b=b&&b.classes?b.classes:"";a=[...(a.classes?a.classes:"").split(" "),...b.split(" ")];a=new Set(a);return Array.from(a).join(" ").trim()}_setOptions(a={}){let b=this.tour&&this.tour.options&&this.tour.options.defaultStepOptions;b=Ma({},b||{});this.options=Object.assign({arrow:!0},
b,a,{floatingUIOptions:Ma(b.floatingUIOptions||{},a.floatingUIOptions||{})});let {when:c}=this.options;this.options.classes=this._getClassOptions(a);this.destroy();this.id=this.options.id||`step-${Ga()}`;c&&Object.keys(c).forEach(d=>{this.on(d,c[d],this)})}_setupElements(){void 0!==this.el&&this.destroy();this.el=this._createTooltipContent();this.options.advanceOn&&Fb(this);Qb(this)}_show(){this.trigger("before-show");this._resolveAttachToOptions();this._setupElements();this.tour.modal||this.tour._setupModal();
this.tour.modal.setupForStep(this);this._styleTargetElementForStep(this);this.el.hidden=!1;this.options.scrollTo&&setTimeout(()=>{this._scrollTo(this.options.scrollTo)});this.el.hidden=!1;let a=this.shepherdElementComponent.getElement(),b=this.target||document.body;b.classList.add(`${this.classPrefix}shepherd-enabled`);b.classList.add(`${this.classPrefix}shepherd-target`);a.classList.add("shepherd-enabled");this.trigger("show")}_styleTargetElementForStep(a){let b=a.target;b&&(a.options.highlightClass&&
b.classList.add(a.options.highlightClass),b.classList.remove("shepherd-target-click-disabled"),!1===a.options.canClickTarget&&b.classList.add("shepherd-target-click-disabled"))}_updateStepTargetOnHide(){let a=this.target||document.body;this.options.highlightClass&&a.classList.remove(this.options.highlightClass);a.classList.remove("shepherd-target-click-disabled",`${this.classPrefix}shepherd-enabled`,`${this.classPrefix}shepherd-target`)}}class Kc extends S{constructor(a){super();Q(this,a,Cc,Bc,O,
{element:0,openingProperties:4,getElement:5,closeModalOpening:6,hide:7,positionModal:8,setupForStep:9,show:10})}get getElement(){return this.$$.ctx[5]}get closeModalOpening(){return this.$$.ctx[6]}get hide(){return this.$$.ctx[7]}get positionModal(){return this.$$.ctx[8]}get setupForStep(){return this.$$.ctx[9]}get show(){return this.$$.ctx[10]}}let ea=new Sa;class Lc extends Sa{constructor(a={}){super(a);Wa(this);this.options=Object.assign({},{exitOnEsc:!0,keyboardNavigation:!0},a);this.classPrefix=
Xa(this.options.classPrefix);this.steps=[];this.addSteps(this.options.steps);"active cancel complete inactive show start".split(" ").map(b=>{(c=>{this.on(c,d=>{d=d||{};d.tour=this;ea.trigger(c,d)})})(b)});this._setTourID();return this}addStep(a,b){a instanceof Ta?a.tour=this:a=new Ta(this,a);void 0!==b?this.steps.splice(b,0,a):this.steps.push(a);return a}addSteps(a){Array.isArray(a)&&a.forEach(b=>{this.addStep(b)});return this}back(){let a=this.steps.indexOf(this.currentStep);this.show(a-1,!1)}async cancel(){if(this.options.confirmCancel){let a=
this.options.confirmCancelMessage||"Are you sure you want to stop the tour?";("function"===typeof this.options.confirmCancel?await this.options.confirmCancel():window.confirm(a))&&this._done("cancel")}else this._done("cancel")}complete(){this._done("complete")}getById(a){return this.steps.find(b=>b.id===a)}getCurrentStep(){return this.currentStep}hide(){let a=this.getCurrentStep();if(a)return a.hide()}isActive(){return ea.activeTour===this}next(){let a=this.steps.indexOf(this.currentStep);a===this.steps.length-
1?this.complete():this.show(a+1,!0)}removeStep(a){let b=this.getCurrentStep();this.steps.some((c,d)=>{if(c.id===a)return c.isOpen()&&c.hide(),c.destroy(),this.steps.splice(d,1),!0});b&&b.id===a&&(this.currentStep=void 0,this.steps.length?this.show(0):this.cancel())}show(a=0,b=!0){if(a=sa(a)?this.getById(a):this.steps[a])this._updateStateBeforeShow(),X(a.options.showOn)&&!a.options.showOn()?this._skipStep(a,b):(this.trigger("show",{step:a,previous:this.currentStep}),this.currentStep=a,a.show())}start(){this.trigger("start");
this.focusedElBeforeOpen=document.activeElement;this.currentStep=null;this._setupModal();this._setupActiveTour();this.next()}_done(a){let b=this.steps.indexOf(this.currentStep);Array.isArray(this.steps)&&this.steps.forEach(c=>c.destroy());zc(this);this.trigger(a,{index:b});ea.activeTour=null;this.trigger("inactive",{tour:this});this.modal&&this.modal.hide();"cancel"!==a&&"complete"!==a||!this.modal||(a=document.querySelector(".shepherd-modal-overlay-container"))&&a.remove();this.focusedElBeforeOpen instanceof
HTMLElement&&this.focusedElBeforeOpen.focus()}_setupActiveTour(){this.trigger("active",{tour:this});ea.activeTour=this}_setupModal(){this.modal=new Kc({target:this.options.modalContainer||document.body,props:{classPrefix:this.classPrefix,styles:this.styles}})}_skipStep(a,b){a=this.steps.indexOf(a);a===this.steps.length-1?this.complete():this.show(b?a+1:a-1,b)}_updateStateBeforeShow(){this.currentStep&&this.currentStep.hide();this.isActive()||this._setupActiveTour()}_setTourID(){this.id=`${this.options.tourName||
"tour"}--${Ga()}`}}class Ab{constructor(){}}"undefined"===typeof window?Object.assign(ea,{Tour:Ab,Step:Ab}):Object.assign(ea,{Tour:Lc,Step:Ta});return ea})
//# sourceMappingURL=shepherd.min.js.map
