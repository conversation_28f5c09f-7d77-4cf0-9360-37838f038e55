<!doctype html>
<html lang="en" data-layout="vertical" data-topbar="light" data-sidebar="dark" data-sidebar-size="lg" data-sidebar-image="none" data-preloader="disable" data-theme="default" data-theme-colors="default">


<!-- Mirrored from themesbrand.com/velzon/html/master/icons-crypto.html by HTTrack Website Copier/3.x [XR&CO'2014], Fri, 23 Aug 2024 16:38:11 GMT -->
<head>

    <meta charset="utf-8" />
    <title>Crypto Icons | Velzon - Admin & Dashboard Template</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta content="Premium Multipurpose Admin & Dashboard Template" name="description" />
    <meta content="Themesbrand" name="author" />
    <!-- App favicon -->
    <link rel="shortcut icon" href="assets/images/favicon.ico">

    <!-- Layout config Js -->
    <script src="assets/js/layout.js"></script>
    <!-- Bootstrap Css -->
    <link href="assets/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <!-- Icons Css -->
    <link href="assets/css/icons.min.css" rel="stylesheet" type="text/css" />
    <!-- App Css-->
    <link href="assets/css/app.min.css" rel="stylesheet" type="text/css" />
    <!-- custom Css-->
    <link href="assets/css/custom.min.css" rel="stylesheet" type="text/css" />

</head>

<body>

    <!-- Begin page -->
    <div id="layout-wrapper">

        <header id="page-topbar">
    <div class="layout-width">
        <div class="navbar-header">
            <div class="d-flex">
                <!-- LOGO -->
                <div class="navbar-brand-box horizontal-logo">
                    <a href="index.html" class="logo logo-dark">
                        <span class="logo-sm">
                            <img src="assets/images/logo-sm.png" alt="" height="22">
                        </span>
                        <span class="logo-lg">
                            <img src="assets/images/logo-dark.png" alt="" height="17">
                        </span>
                    </a>

                    <a href="index.html" class="logo logo-light">
                        <span class="logo-sm">
                            <img src="assets/images/logo-sm.png" alt="" height="22">
                        </span>
                        <span class="logo-lg">
                            <img src="assets/images/logo-light.png" alt="" height="17">
                        </span>
                    </a>
                </div>

                <button type="button" class="btn btn-sm px-3 fs-16 header-item vertical-menu-btn topnav-hamburger material-shadow-none" id="topnav-hamburger-icon">
                    <span class="hamburger-icon">
                        <span></span>
                        <span></span>
                        <span></span>
                    </span>
                </button>

                <!-- App Search-->
                <form class="app-search d-none d-md-block">
                    <div class="position-relative">
                        <input type="text" class="form-control" placeholder="Search..." autocomplete="off" id="search-options" value="">
                        <span class="mdi mdi-magnify search-widget-icon"></span>
                        <span class="mdi mdi-close-circle search-widget-icon search-widget-icon-close d-none" id="search-close-options"></span>
                    </div>
                    <div class="dropdown-menu dropdown-menu-lg" id="search-dropdown">
                        <div data-simplebar style="max-height: 320px;">
                            <!-- item-->
                            <div class="dropdown-header">
                                <h6 class="text-overflow text-muted mb-0 text-uppercase">Recent Searches</h6>
                            </div>

                            <div class="dropdown-item bg-transparent text-wrap">
                                <a href="index.html" class="btn btn-soft-secondary btn-sm rounded-pill">how to setup <i class="mdi mdi-magnify ms-1"></i></a>
                                <a href="index.html" class="btn btn-soft-secondary btn-sm rounded-pill">buttons <i class="mdi mdi-magnify ms-1"></i></a>
                            </div>
                            <!-- item-->
                            <div class="dropdown-header mt-2">
                                <h6 class="text-overflow text-muted mb-1 text-uppercase">Pages</h6>
                            </div>

                            <!-- item-->
                            <a href="javascript:void(0);" class="dropdown-item notify-item">
                                <i class="ri-bubble-chart-line align-middle fs-18 text-muted me-2"></i>
                                <span>Analytics Dashboard</span>
                            </a>

                            <!-- item-->
                            <a href="javascript:void(0);" class="dropdown-item notify-item">
                                <i class="ri-lifebuoy-line align-middle fs-18 text-muted me-2"></i>
                                <span>Help Center</span>
                            </a>

                            <!-- item-->
                            <a href="javascript:void(0);" class="dropdown-item notify-item">
                                <i class="ri-user-settings-line align-middle fs-18 text-muted me-2"></i>
                                <span>My account settings</span>
                            </a>

                            <!-- item-->
                            <div class="dropdown-header mt-2">
                                <h6 class="text-overflow text-muted mb-2 text-uppercase">Members</h6>
                            </div>

                            <div class="notification-list">
                                <!-- item -->
                                <a href="javascript:void(0);" class="dropdown-item notify-item py-2">
                                    <div class="d-flex">
                                        <img src="assets/images/users/avatar-2.jpg" class="me-3 rounded-circle avatar-xs" alt="user-pic">
                                        <div class="flex-grow-1">
                                            <h6 class="m-0">Angela Bernier</h6>
                                            <span class="fs-11 mb-0 text-muted">Manager</span>
                                        </div>
                                    </div>
                                </a>
                                <!-- item -->
                                <a href="javascript:void(0);" class="dropdown-item notify-item py-2">
                                    <div class="d-flex">
                                        <img src="assets/images/users/avatar-3.jpg" class="me-3 rounded-circle avatar-xs" alt="user-pic">
                                        <div class="flex-grow-1">
                                            <h6 class="m-0">David Grasso</h6>
                                            <span class="fs-11 mb-0 text-muted">Web Designer</span>
                                        </div>
                                    </div>
                                </a>
                                <!-- item -->
                                <a href="javascript:void(0);" class="dropdown-item notify-item py-2">
                                    <div class="d-flex">
                                        <img src="assets/images/users/avatar-5.jpg" class="me-3 rounded-circle avatar-xs" alt="user-pic">
                                        <div class="flex-grow-1">
                                            <h6 class="m-0">Mike Bunch</h6>
                                            <span class="fs-11 mb-0 text-muted">React Developer</span>
                                        </div>
                                    </div>
                                </a>
                            </div>
                        </div>

                        <div class="text-center pt-3 pb-1">
                            <a href="pages-search-results.html" class="btn btn-primary btn-sm">View All Results <i class="ri-arrow-right-line ms-1"></i></a>
                        </div>
                    </div>
                </form>
            </div>

            <div class="d-flex align-items-center">

                <div class="dropdown d-md-none topbar-head-dropdown header-item">
                    <button type="button" class="btn btn-icon btn-topbar material-shadow-none btn-ghost-secondary rounded-circle" id="page-header-search-dropdown" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                        <i class="bx bx-search fs-22"></i>
                    </button>
                    <div class="dropdown-menu dropdown-menu-lg dropdown-menu-end p-0" aria-labelledby="page-header-search-dropdown">
                        <form class="p-3">
                            <div class="form-group m-0">
                                <div class="input-group">
                                    <input type="text" class="form-control" placeholder="Search ..." aria-label="Recipient's username">
                                    <button class="btn btn-primary" type="submit"><i class="mdi mdi-magnify"></i></button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <div class="dropdown ms-1 topbar-head-dropdown header-item">
                    <button type="button" class="btn btn-icon btn-topbar material-shadow-none btn-ghost-secondary rounded-circle" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                        <img id="header-lang-img" src="assets/images/flags/us.svg" alt="Header Language" height="20" class="rounded">
                    </button>
                    <div class="dropdown-menu dropdown-menu-end">

                        <!-- item-->
                        <a href="javascript:void(0);" class="dropdown-item notify-item language py-2" data-lang="en" title="English">
                            <img src="assets/images/flags/us.svg" alt="user-image" class="me-2 rounded" height="18">
                            <span class="align-middle">English</span>
                        </a>

                        <!-- item-->
                        <a href="javascript:void(0);" class="dropdown-item notify-item language" data-lang="sp" title="Spanish">
                            <img src="assets/images/flags/spain.svg" alt="user-image" class="me-2 rounded" height="18">
                            <span class="align-middle">Española</span>
                        </a>

                        <!-- item-->
                        <a href="javascript:void(0);" class="dropdown-item notify-item language" data-lang="gr" title="German">
                            <img src="assets/images/flags/germany.svg" alt="user-image" class="me-2 rounded" height="18"> <span class="align-middle">Deutsche</span>
                        </a>

                        <!-- item-->
                        <a href="javascript:void(0);" class="dropdown-item notify-item language" data-lang="it" title="Italian">
                            <img src="assets/images/flags/italy.svg" alt="user-image" class="me-2 rounded" height="18">
                            <span class="align-middle">Italiana</span>
                        </a>

                        <!-- item-->
                        <a href="javascript:void(0);" class="dropdown-item notify-item language" data-lang="ru" title="Russian">
                            <img src="assets/images/flags/russia.svg" alt="user-image" class="me-2 rounded" height="18">
                            <span class="align-middle">русский</span>
                        </a>

                        <!-- item-->
                        <a href="javascript:void(0);" class="dropdown-item notify-item language" data-lang="ch" title="Chinese">
                            <img src="assets/images/flags/china.svg" alt="user-image" class="me-2 rounded" height="18">
                            <span class="align-middle">中国人</span>
                        </a>

                        <!-- item-->
                        <a href="javascript:void(0);" class="dropdown-item notify-item language" data-lang="fr" title="French">
                            <img src="assets/images/flags/french.svg" alt="user-image" class="me-2 rounded" height="18">
                            <span class="align-middle">français</span>
                        </a>

                        <!-- item-->
                        <a href="javascript:void(0);" class="dropdown-item notify-item language" data-lang="ar" title="Arabic">
                            <img src="assets/images/flags/ae.svg" alt="user-image" class="me-2 rounded" height="18">
                            <span class="align-middle">Arabic</span>
                        </a>
                    </div>
                </div>

                <div class="dropdown topbar-head-dropdown ms-1 header-item">
                    <button type="button" class="btn btn-icon btn-topbar material-shadow-none btn-ghost-secondary rounded-circle" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                        <i class='bx bx-category-alt fs-22'></i>
                    </button>
                    <div class="dropdown-menu dropdown-menu-lg p-0 dropdown-menu-end">
                        <div class="p-3 border-top-0 border-start-0 border-end-0 border-dashed border">
                            <div class="row align-items-center">
                                <div class="col">
                                    <h6 class="m-0 fw-semibold fs-15"> Web Apps </h6>
                                </div>
                                <div class="col-auto">
                                    <a href="#!" class="btn btn-sm btn-soft-info"> View All Apps
                                        <i class="ri-arrow-right-s-line align-middle"></i></a>
                                </div>
                            </div>
                        </div>

                        <div class="p-2">
                            <div class="row g-0">
                                <div class="col">
                                    <a class="dropdown-icon-item" href="#!">
                                        <img src="assets/images/brands/github.png" alt="Github">
                                        <span>GitHub</span>
                                    </a>
                                </div>
                                <div class="col">
                                    <a class="dropdown-icon-item" href="#!">
                                        <img src="assets/images/brands/bitbucket.png" alt="bitbucket">
                                        <span>Bitbucket</span>
                                    </a>
                                </div>
                                <div class="col">
                                    <a class="dropdown-icon-item" href="#!">
                                        <img src="assets/images/brands/dribbble.png" alt="dribbble">
                                        <span>Dribbble</span>
                                    </a>
                                </div>
                            </div>

                            <div class="row g-0">
                                <div class="col">
                                    <a class="dropdown-icon-item" href="#!">
                                        <img src="assets/images/brands/dropbox.png" alt="dropbox">
                                        <span>Dropbox</span>
                                    </a>
                                </div>
                                <div class="col">
                                    <a class="dropdown-icon-item" href="#!">
                                        <img src="assets/images/brands/mail_chimp.png" alt="mail_chimp">
                                        <span>Mail Chimp</span>
                                    </a>
                                </div>
                                <div class="col">
                                    <a class="dropdown-icon-item" href="#!">
                                        <img src="assets/images/brands/slack.png" alt="slack">
                                        <span>Slack</span>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="dropdown topbar-head-dropdown ms-1 header-item">
                    <button type="button" class="btn btn-icon btn-topbar material-shadow-none btn-ghost-secondary rounded-circle" id="page-header-cart-dropdown" data-bs-toggle="dropdown" data-bs-auto-close="outside" aria-haspopup="true" aria-expanded="false">
                        <i class='bx bx-shopping-bag fs-22'></i>
                        <span class="position-absolute topbar-badge cartitem-badge fs-10 translate-middle badge rounded-pill bg-info">5</span>
                    </button>
                    <div class="dropdown-menu dropdown-menu-xl dropdown-menu-end p-0 dropdown-menu-cart" aria-labelledby="page-header-cart-dropdown">
                        <div class="p-3 border-top-0 border-start-0 border-end-0 border-dashed border">
                            <div class="row align-items-center">
                                <div class="col">
                                    <h6 class="m-0 fs-16 fw-semibold"> My Cart</h6>
                                </div>
                                <div class="col-auto">
                                    <span class="badge bg-warning-subtle text-warning fs-13"><span class="cartitem-badge">7</span>
                                        items</span>
                                </div>
                            </div>
                        </div>
                        <div data-simplebar style="max-height: 300px;">
                            <div class="p-2">
                                <div class="text-center empty-cart" id="empty-cart">
                                    <div class="avatar-md mx-auto my-3">
                                        <div class="avatar-title bg-info-subtle text-info fs-36 rounded-circle">
                                            <i class='bx bx-cart'></i>
                                        </div>
                                    </div>
                                    <h5 class="mb-3">Your Cart is Empty!</h5>
                                    <a href="apps-ecommerce-products.html" class="btn btn-success w-md mb-3">Shop Now</a>
                                </div>
                                <div class="d-block dropdown-item dropdown-item-cart text-wrap px-3 py-2">
                                    <div class="d-flex align-items-center">
                                        <img src="assets/images/products/img-1.png" class="me-3 rounded-circle avatar-sm p-2 bg-light" alt="user-pic">
                                        <div class="flex-grow-1">
                                            <h6 class="mt-0 mb-1 fs-14">
                                                <a href="apps-ecommerce-product-details.html" class="text-reset">Branded
                                                    T-Shirts</a>
                                            </h6>
                                            <p class="mb-0 fs-12 text-muted">
                                                Quantity: <span>10 x $32</span>
                                            </p>
                                        </div>
                                        <div class="px-2">
                                            <h5 class="m-0 fw-normal">$<span class="cart-item-price">320</span></h5>
                                        </div>
                                        <div class="ps-2">
                                            <button type="button" class="btn btn-icon btn-sm btn-ghost-secondary remove-item-btn"><i class="ri-close-fill fs-16"></i></button>
                                        </div>
                                    </div>
                                </div>

                                <div class="d-block dropdown-item dropdown-item-cart text-wrap px-3 py-2">
                                    <div class="d-flex align-items-center">
                                        <img src="assets/images/products/img-2.png" class="me-3 rounded-circle avatar-sm p-2 bg-light" alt="user-pic">
                                        <div class="flex-grow-1">
                                            <h6 class="mt-0 mb-1 fs-14">
                                                <a href="apps-ecommerce-product-details.html" class="text-reset">Bentwood Chair</a>
                                            </h6>
                                            <p class="mb-0 fs-12 text-muted">
                                                Quantity: <span>5 x $18</span>
                                            </p>
                                        </div>
                                        <div class="px-2">
                                            <h5 class="m-0 fw-normal">$<span class="cart-item-price">89</span></h5>
                                        </div>
                                        <div class="ps-2">
                                            <button type="button" class="btn btn-icon btn-sm btn-ghost-secondary remove-item-btn"><i class="ri-close-fill fs-16"></i></button>
                                        </div>
                                    </div>
                                </div>

                                <div class="d-block dropdown-item dropdown-item-cart text-wrap px-3 py-2">
                                    <div class="d-flex align-items-center">
                                        <img src="assets/images/products/img-3.png" class="me-3 rounded-circle avatar-sm p-2 bg-light" alt="user-pic">
                                        <div class="flex-grow-1">
                                            <h6 class="mt-0 mb-1 fs-14">
                                                <a href="apps-ecommerce-product-details.html" class="text-reset">
                                                    Borosil Paper Cup</a>
                                            </h6>
                                            <p class="mb-0 fs-12 text-muted">
                                                Quantity: <span>3 x $250</span>
                                            </p>
                                        </div>
                                        <div class="px-2">
                                            <h5 class="m-0 fw-normal">$<span class="cart-item-price">750</span></h5>
                                        </div>
                                        <div class="ps-2">
                                            <button type="button" class="btn btn-icon btn-sm btn-ghost-secondary remove-item-btn"><i class="ri-close-fill fs-16"></i></button>
                                        </div>
                                    </div>
                                </div>

                                <div class="d-block dropdown-item dropdown-item-cart text-wrap px-3 py-2">
                                    <div class="d-flex align-items-center">
                                        <img src="assets/images/products/img-6.png" class="me-3 rounded-circle avatar-sm p-2 bg-light" alt="user-pic">
                                        <div class="flex-grow-1">
                                            <h6 class="mt-0 mb-1 fs-14">
                                                <a href="apps-ecommerce-product-details.html" class="text-reset">Gray
                                                    Styled T-Shirt</a>
                                            </h6>
                                            <p class="mb-0 fs-12 text-muted">
                                                Quantity: <span>1 x $1250</span>
                                            </p>
                                        </div>
                                        <div class="px-2">
                                            <h5 class="m-0 fw-normal">$ <span class="cart-item-price">1250</span></h5>
                                        </div>
                                        <div class="ps-2">
                                            <button type="button" class="btn btn-icon btn-sm btn-ghost-secondary remove-item-btn"><i class="ri-close-fill fs-16"></i></button>
                                        </div>
                                    </div>
                                </div>

                                <div class="d-block dropdown-item dropdown-item-cart text-wrap px-3 py-2">
                                    <div class="d-flex align-items-center">
                                        <img src="assets/images/products/img-5.png" class="me-3 rounded-circle avatar-sm p-2 bg-light" alt="user-pic">
                                        <div class="flex-grow-1">
                                            <h6 class="mt-0 mb-1 fs-14">
                                                <a href="apps-ecommerce-product-details.html" class="text-reset">Stillbird Helmet</a>
                                            </h6>
                                            <p class="mb-0 fs-12 text-muted">
                                                Quantity: <span>2 x $495</span>
                                            </p>
                                        </div>
                                        <div class="px-2">
                                            <h5 class="m-0 fw-normal">$<span class="cart-item-price">990</span></h5>
                                        </div>
                                        <div class="ps-2">
                                            <button type="button" class="btn btn-icon btn-sm btn-ghost-secondary remove-item-btn"><i class="ri-close-fill fs-16"></i></button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="p-3 border-bottom-0 border-start-0 border-end-0 border-dashed border" id="checkout-elem">
                            <div class="d-flex justify-content-between align-items-center pb-3">
                                <h5 class="m-0 text-muted">Total:</h5>
                                <div class="px-2">
                                    <h5 class="m-0" id="cart-item-total">$1258.58</h5>
                                </div>
                            </div>

                            <a href="apps-ecommerce-checkout.html" class="btn btn-success text-center w-100">
                                Checkout
                            </a>
                        </div>
                    </div>
                </div>

                <div class="ms-1 header-item d-none d-sm-flex">
                    <button type="button" class="btn btn-icon btn-topbar material-shadow-none btn-ghost-secondary rounded-circle" data-toggle="fullscreen">
                        <i class='bx bx-fullscreen fs-22'></i>
                    </button>
                </div>

                <div class="ms-1 header-item d-none d-sm-flex">
                    <button type="button" class="btn btn-icon btn-topbar material-shadow-none btn-ghost-secondary rounded-circle light-dark-mode">
                        <i class='bx bx-moon fs-22'></i>
                    </button>
                </div>

                <div class="dropdown topbar-head-dropdown ms-1 header-item" id="notificationDropdown">
                    <button type="button" class="btn btn-icon btn-topbar material-shadow-none btn-ghost-secondary rounded-circle" id="page-header-notifications-dropdown" data-bs-toggle="dropdown" data-bs-auto-close="outside" aria-haspopup="true" aria-expanded="false">
                        <i class='bx bx-bell fs-22'></i>
                        <span class="position-absolute topbar-badge fs-10 translate-middle badge rounded-pill bg-danger">3<span class="visually-hidden">unread messages</span></span>
                    </button>
                    <div class="dropdown-menu dropdown-menu-lg dropdown-menu-end p-0" aria-labelledby="page-header-notifications-dropdown">

                        <div class="dropdown-head bg-primary bg-pattern rounded-top">
                            <div class="p-3">
                                <div class="row align-items-center">
                                    <div class="col">
                                        <h6 class="m-0 fs-16 fw-semibold text-white"> Notifications </h6>
                                    </div>
                                    <div class="col-auto dropdown-tabs">
                                        <span class="badge bg-light text-body fs-13"> 4 New</span>
                                    </div>
                                </div>
                            </div>

                            <div class="px-2 pt-2">
                                <ul class="nav nav-tabs dropdown-tabs nav-tabs-custom" data-dropdown-tabs="true" id="notificationItemsTab" role="tablist">
                                    <li class="nav-item waves-effect waves-light">
                                        <a class="nav-link active" data-bs-toggle="tab" href="#all-noti-tab" role="tab" aria-selected="true">
                                            All (4)
                                        </a>
                                    </li>
                                    <li class="nav-item waves-effect waves-light">
                                        <a class="nav-link" data-bs-toggle="tab" href="#messages-tab" role="tab" aria-selected="false">
                                            Messages
                                        </a>
                                    </li>
                                    <li class="nav-item waves-effect waves-light">
                                        <a class="nav-link" data-bs-toggle="tab" href="#alerts-tab" role="tab" aria-selected="false">
                                            Alerts
                                        </a>
                                    </li>
                                </ul>
                            </div>

                        </div>

                        <div class="tab-content position-relative" id="notificationItemsTabContent">
                            <div class="tab-pane fade show active py-2 ps-2" id="all-noti-tab" role="tabpanel">
                                <div data-simplebar style="max-height: 300px;" class="pe-2">
                                    <div class="text-reset notification-item d-block dropdown-item position-relative">
                                        <div class="d-flex">
                                            <div class="avatar-xs me-3 flex-shrink-0">
                                                <span class="avatar-title bg-info-subtle text-info rounded-circle fs-16">
                                                    <i class="bx bx-badge-check"></i>
                                                </span>
                                            </div>
                                            <div class="flex-grow-1">
                                                <a href="#!" class="stretched-link">
                                                    <h6 class="mt-0 mb-2 lh-base">Your <b>Elite</b> author Graphic
                                                        Optimization <span class="text-secondary">reward</span> is
                                                        ready!
                                                    </h6>
                                                </a>
                                                <p class="mb-0 fs-11 fw-medium text-uppercase text-muted">
                                                    <span><i class="mdi mdi-clock-outline"></i> Just 30 sec ago</span>
                                                </p>
                                            </div>
                                            <div class="px-2 fs-15">
                                                <div class="form-check notification-check">
                                                    <input class="form-check-input" type="checkbox" value="" id="all-notification-check01">
                                                    <label class="form-check-label" for="all-notification-check01"></label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="text-reset notification-item d-block dropdown-item position-relative">
                                        <div class="d-flex">
                                            <img src="assets/images/users/avatar-2.jpg" class="me-3 rounded-circle avatar-xs flex-shrink-0" alt="user-pic">
                                            <div class="flex-grow-1">
                                                <a href="#!" class="stretched-link">
                                                    <h6 class="mt-0 mb-1 fs-13 fw-semibold">Angela Bernier</h6>
                                                </a>
                                                <div class="fs-13 text-muted">
                                                    <p class="mb-1">Answered to your comment on the cash flow forecast's
                                                        graph 🔔.</p>
                                                </div>
                                                <p class="mb-0 fs-11 fw-medium text-uppercase text-muted">
                                                    <span><i class="mdi mdi-clock-outline"></i> 48 min ago</span>
                                                </p>
                                            </div>
                                            <div class="px-2 fs-15">
                                                <div class="form-check notification-check">
                                                    <input class="form-check-input" type="checkbox" value="" id="all-notification-check02">
                                                    <label class="form-check-label" for="all-notification-check02"></label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="text-reset notification-item d-block dropdown-item position-relative">
                                        <div class="d-flex">
                                            <div class="avatar-xs me-3 flex-shrink-0">
                                                <span class="avatar-title bg-danger-subtle text-danger rounded-circle fs-16">
                                                    <i class='bx bx-message-square-dots'></i>
                                                </span>
                                            </div>
                                            <div class="flex-grow-1">
                                                <a href="#!" class="stretched-link">
                                                    <h6 class="mt-0 mb-2 fs-13 lh-base">You have received <b class="text-success">20</b> new messages in the conversation
                                                    </h6>
                                                </a>
                                                <p class="mb-0 fs-11 fw-medium text-uppercase text-muted">
                                                    <span><i class="mdi mdi-clock-outline"></i> 2 hrs ago</span>
                                                </p>
                                            </div>
                                            <div class="px-2 fs-15">
                                                <div class="form-check notification-check">
                                                    <input class="form-check-input" type="checkbox" value="" id="all-notification-check03">
                                                    <label class="form-check-label" for="all-notification-check03"></label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="text-reset notification-item d-block dropdown-item position-relative">
                                        <div class="d-flex">
                                            <img src="assets/images/users/avatar-8.jpg" class="me-3 rounded-circle avatar-xs flex-shrink-0" alt="user-pic">
                                            <div class="flex-grow-1">
                                                <a href="#!" class="stretched-link">
                                                    <h6 class="mt-0 mb-1 fs-13 fw-semibold">Maureen Gibson</h6>
                                                </a>
                                                <div class="fs-13 text-muted">
                                                    <p class="mb-1">We talked about a project on linkedin.</p>
                                                </div>
                                                <p class="mb-0 fs-11 fw-medium text-uppercase text-muted">
                                                    <span><i class="mdi mdi-clock-outline"></i> 4 hrs ago</span>
                                                </p>
                                            </div>
                                            <div class="px-2 fs-15">
                                                <div class="form-check notification-check">
                                                    <input class="form-check-input" type="checkbox" value="" id="all-notification-check04">
                                                    <label class="form-check-label" for="all-notification-check04"></label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="my-3 text-center view-all">
                                        <button type="button" class="btn btn-soft-success waves-effect waves-light">View
                                            All Notifications <i class="ri-arrow-right-line align-middle"></i></button>
                                    </div>
                                </div>

                            </div>

                            <div class="tab-pane fade py-2 ps-2" id="messages-tab" role="tabpanel" aria-labelledby="messages-tab">
                                <div data-simplebar style="max-height: 300px;" class="pe-2">
                                    <div class="text-reset notification-item d-block dropdown-item">
                                        <div class="d-flex">
                                            <img src="assets/images/users/avatar-3.jpg" class="me-3 rounded-circle avatar-xs" alt="user-pic">
                                            <div class="flex-grow-1">
                                                <a href="#!" class="stretched-link">
                                                    <h6 class="mt-0 mb-1 fs-13 fw-semibold">James Lemire</h6>
                                                </a>
                                                <div class="fs-13 text-muted">
                                                    <p class="mb-1">We talked about a project on linkedin.</p>
                                                </div>
                                                <p class="mb-0 fs-11 fw-medium text-uppercase text-muted">
                                                    <span><i class="mdi mdi-clock-outline"></i> 30 min ago</span>
                                                </p>
                                            </div>
                                            <div class="px-2 fs-15">
                                                <div class="form-check notification-check">
                                                    <input class="form-check-input" type="checkbox" value="" id="messages-notification-check01">
                                                    <label class="form-check-label" for="messages-notification-check01"></label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="text-reset notification-item d-block dropdown-item">
                                        <div class="d-flex">
                                            <img src="assets/images/users/avatar-2.jpg" class="me-3 rounded-circle avatar-xs" alt="user-pic">
                                            <div class="flex-grow-1">
                                                <a href="#!" class="stretched-link">
                                                    <h6 class="mt-0 mb-1 fs-13 fw-semibold">Angela Bernier</h6>
                                                </a>
                                                <div class="fs-13 text-muted">
                                                    <p class="mb-1">Answered to your comment on the cash flow forecast's
                                                        graph 🔔.</p>
                                                </div>
                                                <p class="mb-0 fs-11 fw-medium text-uppercase text-muted">
                                                    <span><i class="mdi mdi-clock-outline"></i> 2 hrs ago</span>
                                                </p>
                                            </div>
                                            <div class="px-2 fs-15">
                                                <div class="form-check notification-check">
                                                    <input class="form-check-input" type="checkbox" value="" id="messages-notification-check02">
                                                    <label class="form-check-label" for="messages-notification-check02"></label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="text-reset notification-item d-block dropdown-item">
                                        <div class="d-flex">
                                            <img src="assets/images/users/avatar-6.jpg" class="me-3 rounded-circle avatar-xs" alt="user-pic">
                                            <div class="flex-grow-1">
                                                <a href="#!" class="stretched-link">
                                                    <h6 class="mt-0 mb-1 fs-13 fw-semibold">Kenneth Brown</h6>
                                                </a>
                                                <div class="fs-13 text-muted">
                                                    <p class="mb-1">Mentionned you in his comment on 📃 invoice #12501.
                                                    </p>
                                                </div>
                                                <p class="mb-0 fs-11 fw-medium text-uppercase text-muted">
                                                    <span><i class="mdi mdi-clock-outline"></i> 10 hrs ago</span>
                                                </p>
                                            </div>
                                            <div class="px-2 fs-15">
                                                <div class="form-check notification-check">
                                                    <input class="form-check-input" type="checkbox" value="" id="messages-notification-check03">
                                                    <label class="form-check-label" for="messages-notification-check03"></label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="text-reset notification-item d-block dropdown-item">
                                        <div class="d-flex">
                                            <img src="assets/images/users/avatar-8.jpg" class="me-3 rounded-circle avatar-xs" alt="user-pic">
                                            <div class="flex-grow-1">
                                                <a href="#!" class="stretched-link">
                                                    <h6 class="mt-0 mb-1 fs-13 fw-semibold">Maureen Gibson</h6>
                                                </a>
                                                <div class="fs-13 text-muted">
                                                    <p class="mb-1">We talked about a project on linkedin.</p>
                                                </div>
                                                <p class="mb-0 fs-11 fw-medium text-uppercase text-muted">
                                                    <span><i class="mdi mdi-clock-outline"></i> 3 days ago</span>
                                                </p>
                                            </div>
                                            <div class="px-2 fs-15">
                                                <div class="form-check notification-check">
                                                    <input class="form-check-input" type="checkbox" value="" id="messages-notification-check04">
                                                    <label class="form-check-label" for="messages-notification-check04"></label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="my-3 text-center view-all">
                                        <button type="button" class="btn btn-soft-success waves-effect waves-light">View
                                            All Messages <i class="ri-arrow-right-line align-middle"></i></button>
                                    </div>
                                </div>
                            </div>
                            <div class="tab-pane fade p-4" id="alerts-tab" role="tabpanel" aria-labelledby="alerts-tab"></div>

                            <div class="notification-actions" id="notification-actions">
                                <div class="d-flex text-muted justify-content-center">
                                    Select <div id="select-content" class="text-body fw-semibold px-1">0</div> Result <button type="button" class="btn btn-link link-danger p-0 ms-3" data-bs-toggle="modal" data-bs-target="#removeNotificationModal">Remove</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="dropdown ms-sm-3 header-item topbar-user">
                    <button type="button" class="btn material-shadow-none" id="page-header-user-dropdown" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                        <span class="d-flex align-items-center">
                            <img class="rounded-circle header-profile-user" src="assets/images/users/avatar-1.jpg" alt="Header Avatar">
                            <span class="text-start ms-xl-2">
                                <span class="d-none d-xl-inline-block ms-1 fw-medium user-name-text">Anna Adame</span>
                                <span class="d-none d-xl-block ms-1 fs-12 user-name-sub-text">Founder</span>
                            </span>
                        </span>
                    </button>
                    <div class="dropdown-menu dropdown-menu-end">
                        <!-- item-->
                        <h6 class="dropdown-header">Welcome Anna!</h6>
                        <a class="dropdown-item" href="pages-profile.html"><i class="mdi mdi-account-circle text-muted fs-16 align-middle me-1"></i> <span class="align-middle">Profile</span></a>
                        <a class="dropdown-item" href="apps-chat.html"><i class="mdi mdi-message-text-outline text-muted fs-16 align-middle me-1"></i> <span class="align-middle">Messages</span></a>
                        <a class="dropdown-item" href="apps-tasks-kanban.html"><i class="mdi mdi-calendar-check-outline text-muted fs-16 align-middle me-1"></i> <span class="align-middle">Taskboard</span></a>
                        <a class="dropdown-item" href="pages-faqs.html"><i class="mdi mdi-lifebuoy text-muted fs-16 align-middle me-1"></i> <span class="align-middle">Help</span></a>
                        <div class="dropdown-divider"></div>
                        <a class="dropdown-item" href="pages-profile.html"><i class="mdi mdi-wallet text-muted fs-16 align-middle me-1"></i> <span class="align-middle">Balance : <b>$5971.67</b></span></a>
                        <a class="dropdown-item" href="pages-profile-settings.html"><span class="badge bg-success-subtle text-success mt-1 float-end">New</span><i class="mdi mdi-cog-outline text-muted fs-16 align-middle me-1"></i> <span class="align-middle">Settings</span></a>
                        <a class="dropdown-item" href="auth-lockscreen-basic.html"><i class="mdi mdi-lock text-muted fs-16 align-middle me-1"></i> <span class="align-middle">Lock screen</span></a>
                        <a class="dropdown-item" href="auth-logout-basic.html"><i class="mdi mdi-logout text-muted fs-16 align-middle me-1"></i> <span class="align-middle" data-key="t-logout">Logout</span></a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</header>

<!-- removeNotificationModal -->
<div id="removeNotificationModal" class="modal fade zoomIn" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" id="NotificationModalbtn-close"></button>
            </div>
            <div class="modal-body">
                <div class="mt-2 text-center">
                    <lord-icon src="https://cdn.lordicon.com/gsqxdxog.json" trigger="loop" colors="primary:#f7b84b,secondary:#f06548" style="width:100px;height:100px"></lord-icon>
                    <div class="mt-4 pt-2 fs-15 mx-4 mx-sm-5">
                        <h4>Are you sure ?</h4>
                        <p class="text-muted mx-4 mb-0">Are you sure you want to remove this Notification ?</p>
                    </div>
                </div>
                <div class="d-flex gap-2 justify-content-center mt-4 mb-2">
                    <button type="button" class="btn w-sm btn-light" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn w-sm btn-danger" id="delete-notification">Yes, Delete It!</button>
                </div>
            </div>

        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div><!-- /.modal -->
        <!-- ========== App Menu ========== -->
        <div class="app-menu navbar-menu">
            <!-- LOGO -->
            <div class="navbar-brand-box">
                <!-- Dark Logo-->
                <a href="index.html" class="logo logo-dark">
                    <span class="logo-sm">
                        <img src="assets/images/logo-sm.png" alt="" height="22">
                    </span>
                    <span class="logo-lg">
                        <img src="assets/images/logo-dark.png" alt="" height="17">
                    </span>
                </a>
                <!-- Light Logo-->
                <a href="index.html" class="logo logo-light">
                    <span class="logo-sm">
                        <img src="assets/images/logo-sm.png" alt="" height="22">
                    </span>
                    <span class="logo-lg">
                        <img src="assets/images/logo-light.png" alt="" height="17">
                    </span>
                </a>
                <button type="button" class="btn btn-sm p-0 fs-20 header-item float-end btn-vertical-sm-hover" id="vertical-hover">
                    <i class="ri-record-circle-line"></i>
                </button>
            </div>
    
            <div class="dropdown sidebar-user m-1 rounded">
                <button type="button" class="btn material-shadow-none" id="page-header-user-dropdown" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    <span class="d-flex align-items-center gap-2">
                        <img class="rounded header-profile-user" src="assets/images/users/avatar-1.jpg" alt="Header Avatar">
                        <span class="text-start">
                            <span class="d-block fw-medium sidebar-user-name-text">Anna Adame</span>
                            <span class="d-block fs-14 sidebar-user-name-sub-text"><i class="ri ri-circle-fill fs-10 text-success align-baseline"></i> <span class="align-middle">Online</span></span>
                        </span>
                    </span>
                </button>
                <div class="dropdown-menu dropdown-menu-end">
                    <!-- item-->
                    <h6 class="dropdown-header">Welcome Anna!</h6>
                    <a class="dropdown-item" href="pages-profile.html"><i class="mdi mdi-account-circle text-muted fs-16 align-middle me-1"></i> <span class="align-middle">Profile</span></a>
                    <a class="dropdown-item" href="apps-chat.html"><i class="mdi mdi-message-text-outline text-muted fs-16 align-middle me-1"></i> <span class="align-middle">Messages</span></a>
                    <a class="dropdown-item" href="apps-tasks-kanban.html"><i class="mdi mdi-calendar-check-outline text-muted fs-16 align-middle me-1"></i> <span class="align-middle">Taskboard</span></a>
                    <a class="dropdown-item" href="pages-faqs.html"><i class="mdi mdi-lifebuoy text-muted fs-16 align-middle me-1"></i> <span class="align-middle">Help</span></a>
                    <div class="dropdown-divider"></div>
                    <a class="dropdown-item" href="pages-profile.html"><i class="mdi mdi-wallet text-muted fs-16 align-middle me-1"></i> <span class="align-middle">Balance : <b>$5971.67</b></span></a>
                    <a class="dropdown-item" href="pages-profile-settings.html"><span class="badge bg-success-subtle text-success mt-1 float-end">New</span><i class="mdi mdi-cog-outline text-muted fs-16 align-middle me-1"></i> <span class="align-middle">Settings</span></a>
                    <a class="dropdown-item" href="auth-lockscreen-basic.html"><i class="mdi mdi-lock text-muted fs-16 align-middle me-1"></i> <span class="align-middle">Lock screen</span></a>
                    <a class="dropdown-item" href="auth-logout-basic.html"><i class="mdi mdi-logout text-muted fs-16 align-middle me-1"></i> <span class="align-middle" data-key="t-logout">Logout</span></a>
                </div>
            </div>
            <div id="scrollbar">
                <div class="container-fluid">


                    <div id="two-column-menu">
                    </div>
                    <ul class="navbar-nav" id="navbar-nav">
                        <li class="menu-title"><span data-key="t-menu">Menu</span></li>
                        <li class="nav-item">
                            <a class="nav-link menu-link" href="#sidebarDashboards" data-bs-toggle="collapse" role="button" aria-expanded="false" aria-controls="sidebarDashboards">
                                <i class="ri-dashboard-2-line"></i> <span data-key="t-dashboards">Dashboards</span>
                            </a>
                            <div class="collapse menu-dropdown" id="sidebarDashboards">
                                <ul class="nav nav-sm flex-column">
                                    <li class="nav-item">
                                        <a href="dashboard-analytics.html" class="nav-link" data-key="t-analytics"> Analytics </a>
                                    </li>
                                    <li class="nav-item">
                                        <a href="dashboard-crm.html" class="nav-link" data-key="t-crm"> CRM </a>
                                    </li>
                                    <li class="nav-item">
                                        <a href="index.html" class="nav-link" data-key="t-ecommerce"> Ecommerce </a>
                                    </li>
                                    <li class="nav-item">
                                        <a href="dashboard-crypto.html" class="nav-link" data-key="t-crypto"> Crypto </a>
                                    </li>
                                    <li class="nav-item">
                                        <a href="dashboard-projects.html" class="nav-link" data-key="t-projects"> Projects </a>
                                    </li>
                                    <li class="nav-item">
                                        <a href="dashboard-nft.html" class="nav-link" data-key="t-nft"> NFT</a>
                                    </li>
                                    <li class="nav-item">
                                        <a href="dashboard-job.html" class="nav-link" data-key="t-job">Job</a>
                                    </li>
                                </ul>
                            </div>
                        </li> <!-- end Dashboard Menu -->
                        <li class="nav-item">
                            <a class="nav-link menu-link" href="#sidebarApps" data-bs-toggle="collapse" role="button" aria-expanded="false" aria-controls="sidebarApps">
                                <i class="ri-apps-2-line"></i> <span data-key="t-apps">Apps</span>
                            </a>
                            <div class="collapse menu-dropdown" id="sidebarApps">
                                <ul class="nav nav-sm flex-column">
                                    <li class="nav-item">
                                        <a href="#sidebarCalendar" class="nav-link" data-bs-toggle="collapse" role="button" aria-expanded="false" aria-controls="sidebarCalendar" data-key="t-calender">
                                            Calendar
                                        </a>
                                        <div class="collapse menu-dropdown" id="sidebarCalendar">
                                            <ul class="nav nav-sm flex-column">
                                                <li class="nav-item">
                                                    <a href="apps-calendar.html" class="nav-link" data-key="t-main-calender"> Main Calender </a>
                                                </li>
                                                <li class="nav-item">
                                                    <a href="apps-calendar-month-grid.html" class="nav-link" data-key="t-month-grid"> Month Grid </a>
                                                </li>
                                            </ul>
                                        </div>
                                    </li>
                                    <li class="nav-item">
                                        <a href="apps-chat.html" class="nav-link" data-key="t-chat"> Chat </a>
                                    </li>
                                    <li class="nav-item">
                                        <a href="#sidebarEmail" class="nav-link" data-bs-toggle="collapse" role="button" aria-expanded="false" aria-controls="sidebarEmail" data-key="t-email">
                                            Email
                                        </a>
                                        <div class="collapse menu-dropdown" id="sidebarEmail">
                                            <ul class="nav nav-sm flex-column">
                                                <li class="nav-item">
                                                    <a href="apps-mailbox.html" class="nav-link" data-key="t-mailbox"> Mailbox </a>
                                                </li>
                                                <li class="nav-item">
                                                    <a href="#sidebaremailTemplates" class="nav-link" data-bs-toggle="collapse" role="button" aria-expanded="false" aria-controls="sidebaremailTemplates" data-key="t-email-templates">
                                                        Email Templates
                                                    </a>
                                                    <div class="collapse menu-dropdown" id="sidebaremailTemplates">
                                                        <ul class="nav nav-sm flex-column">
                                                            <li class="nav-item">
                                                                <a href="apps-email-basic.html" class="nav-link" data-key="t-basic-action"> Basic Action </a>
                                                            </li>
                                                            <li class="nav-item">
                                                                <a href="apps-email-ecommerce.html" class="nav-link" data-key="t-ecommerce-action"> Ecommerce Action </a>
                                                            </li>
                                                        </ul>
                                                    </div>
                                                </li>
                                            </ul>
                                        </div>
                                    </li>
                                    <li class="nav-item">
                                        <a href="#sidebarEcommerce" class="nav-link" data-bs-toggle="collapse" role="button" aria-expanded="false" aria-controls="sidebarEcommerce" data-key="t-ecommerce">
                                            Ecommerce
                                        </a>
                                        <div class="collapse menu-dropdown" id="sidebarEcommerce">
                                            <ul class="nav nav-sm flex-column">
                                                <li class="nav-item">
                                                    <a href="apps-ecommerce-products.html" class="nav-link" data-key="t-products"> Products </a>
                                                </li>
                                                <li class="nav-item">
                                                    <a href="apps-ecommerce-product-details.html" class="nav-link" data-key="t-product-Details"> Product Details </a>
                                                </li>
                                                <li class="nav-item">
                                                    <a href="apps-ecommerce-add-product.html" class="nav-link" data-key="t-create-product"> Create Product </a>
                                                </li>
                                                <li class="nav-item">
                                                    <a href="apps-ecommerce-orders.html" class="nav-link" data-key="t-orders">
                                                        Orders </a>
                                                </li>
                                                <li class="nav-item">
                                                    <a href="apps-ecommerce-order-details.html" class="nav-link" data-key="t-order-details"> Order Details </a>
                                                </li>
                                                <li class="nav-item">
                                                    <a href="apps-ecommerce-customers.html" class="nav-link" data-key="t-customers"> Customers </a>
                                                </li>
                                                <li class="nav-item">
                                                    <a href="apps-ecommerce-cart.html" class="nav-link" data-key="t-shopping-cart"> Shopping Cart </a>
                                                </li>
                                                <li class="nav-item">
                                                    <a href="apps-ecommerce-checkout.html" class="nav-link" data-key="t-checkout"> Checkout </a>
                                                </li>
                                                <li class="nav-item">
                                                    <a href="apps-ecommerce-sellers.html" class="nav-link" data-key="t-sellers">
                                                        Sellers </a>
                                                </li>
                                                <li class="nav-item">
                                                    <a href="apps-ecommerce-seller-details.html" class="nav-link" data-key="t-sellers-details"> Seller Details </a>
                                                </li>
                                            </ul>
                                        </div>
                                    </li>
                                    <li class="nav-item">
                                        <a href="#sidebarProjects" class="nav-link" data-bs-toggle="collapse" role="button" aria-expanded="false" aria-controls="sidebarProjects" data-key="t-projects">
                                            Projects
                                        </a>
                                        <div class="collapse menu-dropdown" id="sidebarProjects">
                                            <ul class="nav nav-sm flex-column">
                                                <li class="nav-item">
                                                    <a href="apps-projects-list.html" class="nav-link" data-key="t-list"> List
                                                    </a>
                                                </li>
                                                <li class="nav-item">
                                                    <a href="apps-projects-overview.html" class="nav-link" data-key="t-overview"> Overview </a>
                                                </li>
                                                <li class="nav-item">
                                                    <a href="apps-projects-create.html" class="nav-link" data-key="t-create-project"> Create Project </a>
                                                </li>
                                            </ul>
                                        </div>
                                    </li>
                                    <li class="nav-item">
                                        <a href="#sidebarTasks" class="nav-link" data-bs-toggle="collapse" role="button" aria-expanded="false" aria-controls="sidebarTasks" data-key="t-tasks"> Tasks
                                        </a>
                                        <div class="collapse menu-dropdown" id="sidebarTasks">
                                            <ul class="nav nav-sm flex-column">
                                                <li class="nav-item">
                                                    <a href="apps-tasks-kanban.html" class="nav-link" data-key="t-kanbanboard">
                                                        Kanban Board </a>
                                                </li>
                                                <li class="nav-item">
                                                    <a href="apps-tasks-list-view.html" class="nav-link" data-key="t-list-view">
                                                        List View </a>
                                                </li>
                                                <li class="nav-item">
                                                    <a href="apps-tasks-details.html" class="nav-link" data-key="t-task-details"> Task Details </a>
                                                </li>
                                            </ul>
                                        </div>
                                    </li>
                                    <li class="nav-item">
                                        <a href="#sidebarCRM" class="nav-link" data-bs-toggle="collapse" role="button" aria-expanded="false" aria-controls="sidebarCRM" data-key="t-crm"> CRM
                                        </a>
                                        <div class="collapse menu-dropdown" id="sidebarCRM">
                                            <ul class="nav nav-sm flex-column">
                                                <li class="nav-item">
                                                    <a href="apps-crm-contacts.html" class="nav-link" data-key="t-contacts">
                                                        Contacts </a>
                                                </li>
                                                <li class="nav-item">
                                                    <a href="apps-crm-companies.html" class="nav-link" data-key="t-companies">
                                                        Companies </a>
                                                </li>
                                                <li class="nav-item">
                                                    <a href="apps-crm-deals.html" class="nav-link" data-key="t-deals"> Deals
                                                    </a>
                                                </li>
                                                <li class="nav-item">
                                                    <a href="apps-crm-leads.html" class="nav-link" data-key="t-leads"> Leads
                                                    </a>
                                                </li>
                                            </ul>
                                        </div>
                                    </li>
                                    <li class="nav-item">
                                        <a href="#sidebarCrypto" class="nav-link" data-bs-toggle="collapse" role="button" aria-expanded="false" aria-controls="sidebarCrypto" data-key="t-crypto"> Crypto
                                        </a>
                                        <div class="collapse menu-dropdown" id="sidebarCrypto">
                                            <ul class="nav nav-sm flex-column">
                                                <li class="nav-item">
                                                    <a href="apps-crypto-transactions.html" class="nav-link" data-key="t-transactions"> Transactions </a>
                                                </li>
                                                <li class="nav-item">
                                                    <a href="apps-crypto-buy-sell.html" class="nav-link" data-key="t-buy-sell">
                                                        Buy & Sell </a>
                                                </li>
                                                <li class="nav-item">
                                                    <a href="apps-crypto-orders.html" class="nav-link" data-key="t-orders">
                                                        Orders </a>
                                                </li>
                                                <li class="nav-item">
                                                    <a href="apps-crypto-wallet.html" class="nav-link" data-key="t-my-wallet">
                                                        My Wallet </a>
                                                </li>
                                                <li class="nav-item">
                                                    <a href="apps-crypto-ico.html" class="nav-link" data-key="t-ico-list"> ICO
                                                        List </a>
                                                </li>
                                                <li class="nav-item">
                                                    <a href="apps-crypto-kyc.html" class="nav-link" data-key="t-kyc-application"> KYC Application </a>
                                                </li>
                                            </ul>
                                        </div>
                                    </li>
                                    <li class="nav-item">
                                        <a href="#sidebarInvoices" class="nav-link" data-bs-toggle="collapse" role="button" aria-expanded="false" aria-controls="sidebarInvoices" data-key="t-invoices">
                                            Invoices
                                        </a>
                                        <div class="collapse menu-dropdown" id="sidebarInvoices">
                                            <ul class="nav nav-sm flex-column">
                                                <li class="nav-item">
                                                    <a href="apps-invoices-list.html" class="nav-link" data-key="t-list-view">
                                                        List View </a>
                                                </li>
                                                <li class="nav-item">
                                                    <a href="apps-invoices-details.html" class="nav-link" data-key="t-details">
                                                        Details </a>
                                                </li>
                                                <li class="nav-item">
                                                    <a href="apps-invoices-create.html" class="nav-link" data-key="t-create-invoice"> Create Invoice </a>
                                                </li>
                                            </ul>
                                        </div>
                                    </li>
                                    <li class="nav-item">
                                        <a href="#sidebarTickets" class="nav-link" data-bs-toggle="collapse" role="button" aria-expanded="false" aria-controls="sidebarTickets" data-key="t-supprt-tickets">
                                            Support Tickets
                                        </a>
                                        <div class="collapse menu-dropdown" id="sidebarTickets">
                                            <ul class="nav nav-sm flex-column">
                                                <li class="nav-item">
                                                    <a href="apps-tickets-list.html" class="nav-link" data-key="t-list-view">
                                                        List View </a>
                                                </li>
                                                <li class="nav-item">
                                                    <a href="apps-tickets-details.html" class="nav-link" data-key="t-ticket-details"> Ticket Details </a>
                                                </li>
                                            </ul>
                                        </div>
                                    </li>
                                    <li class="nav-item">
                                        <a href="#sidebarnft" class="nav-link" data-bs-toggle="collapse" role="button" aria-expanded="false" aria-controls="sidebarnft" data-key="t-nft-marketplace">
                                            NFT Marketplace
                                        </a>
                                        <div class="collapse menu-dropdown" id="sidebarnft">
                                            <ul class="nav nav-sm flex-column">
                                                <li class="nav-item">
                                                    <a href="apps-nft-marketplace.html" class="nav-link" data-key="t-marketplace"> Marketplace </a>
                                                </li>
                                                <li class="nav-item">
                                                    <a href="apps-nft-explore.html" class="nav-link" data-key="t-explore-now"> Explore Now </a>
                                                </li>
                                                <li class="nav-item">
                                                    <a href="apps-nft-auction.html" class="nav-link" data-key="t-live-auction"> Live Auction </a>
                                                </li>
                                                <li class="nav-item">
                                                    <a href="apps-nft-item-details.html" class="nav-link" data-key="t-item-details"> Item Details </a>
                                                </li>
                                                <li class="nav-item">
                                                    <a href="apps-nft-collections.html" class="nav-link" data-key="t-collections"> Collections </a>
                                                </li>
                                                <li class="nav-item">
                                                    <a href="apps-nft-creators.html" class="nav-link" data-key="t-creators"> Creators </a>
                                                </li>
                                                <li class="nav-item">
                                                    <a href="apps-nft-ranking.html" class="nav-link" data-key="t-ranking"> Ranking </a>
                                                </li>
                                                <li class="nav-item">
                                                    <a href="apps-nft-wallet.html" class="nav-link" data-key="t-wallet-connect"> Wallet Connect </a>
                                                </li>
                                                <li class="nav-item">
                                                    <a href="apps-nft-create.html" class="nav-link" data-key="t-create-nft"> Create NFT </a>
                                                </li>
                                            </ul>
                                        </div>
                                    </li>
                                    <li class="nav-item">
                                        <a href="apps-file-manager.html" class="nav-link"> <span data-key="t-file-manager">File Manager</span></a>
                                    </li>
                                    <li class="nav-item">
                                        <a href="apps-todo.html" class="nav-link"> <span data-key="t-to-do">To Do</span></a>
                                    </li>
                                    <li class="nav-item">
                                        <a href="#sidebarjobs" class="nav-link" data-bs-toggle="collapse" role="button" aria-expanded="false" aria-controls="sidebarjobs" data-key="t-jobs"> Jobs</a>
                                        <div class="collapse menu-dropdown" id="sidebarjobs">
                                            <ul class="nav nav-sm flex-column">
                                                <li class="nav-item">
                                                    <a href="apps-job-statistics.html" class="nav-link" data-key="t-statistics"> Statistics </a>
                                                </li>
                                                <li class="nav-item">
                                                    <a href="#sidebarJoblists" class="nav-link" data-bs-toggle="collapse" role="button" aria-expanded="false" aria-controls="sidebarJoblists" data-key="t-job-lists">
                                                        Job Lists
                                                    </a>
                                                    <div class="collapse menu-dropdown" id="sidebarJoblists">
                                                        <ul class="nav nav-sm flex-column">
                                                            <li class="nav-item">
                                                                <a href="apps-job-lists.html" class="nav-link" data-key="t-list"> List
                                                                </a>
                                                            </li>
                                                            <li class="nav-item">
                                                                <a href="apps-job-grid-lists.html" class="nav-link" data-key="t-grid"> Grid </a>
                                                            </li>
                                                            <li class="nav-item">
                                                                <a href="apps-job-details.html" class="nav-link" data-key="t-overview"> Overview</a>
                                                            </li>
                                                        </ul>
                                                    </div>
                                                </li>
                                                <li class="nav-item">
                                                    <a href="#sidebarCandidatelists" class="nav-link" data-bs-toggle="collapse" role="button" aria-expanded="false" aria-controls="sidebarCandidatelists" data-key="t-candidate-lists">
                                                        Candidate Lists
                                                    </a>
                                                    <div class="collapse menu-dropdown" id="sidebarCandidatelists">
                                                        <ul class="nav nav-sm flex-column">
                                                            <li class="nav-item">
                                                                <a href="apps-job-candidate-lists.html" class="nav-link" data-key="t-list-view"> List View
                                                                </a>
                                                            </li>
                                                            <li class="nav-item">
                                                                <a href="apps-job-candidate-grid.html" class="nav-link" data-key="t-grid-view"> Grid View</a>
                                                            </li>
                                                        </ul>
                                                    </div>
                                                </li>
                                                <li class="nav-item">
                                                    <a href="apps-job-application.html" class="nav-link" data-key="t-application"> Application </a>
                                                </li>
                                                <li class="nav-item">
                                                    <a href="apps-job-new.html" class="nav-link" data-key="t-new-job"> New Job </a>
                                                </li>
                                                <li class="nav-item">
                                                    <a href="apps-job-companies-lists.html" class="nav-link" data-key="t-companies-list"> Companies List </a>
                                                </li>
                                                <li class="nav-item">
                                                    <a href="apps-job-categories.html" class="nav-link" data-key="t-job-categories"> Job Categories</a>
                                                </li>
                                            </ul>
                                        </div>
                                    </li>
                                    <li class="nav-item">
                                        <a href="apps-api-key.html" class="nav-link" data-key="t-api-key">API Key</a>
                                    </li>
                                </ul>
                            </div>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link menu-link" href="#sidebarLayouts" data-bs-toggle="collapse" role="button" aria-expanded="false" aria-controls="sidebarLayouts">
                                <i class="ri-layout-3-line"></i> <span data-key="t-layouts">Layouts</span> <span class="badge badge-pill bg-danger" data-key="t-hot">Hot</span>
                            </a>
                            <div class="collapse menu-dropdown" id="sidebarLayouts">
                                <ul class="nav nav-sm flex-column">
                                    <li class="nav-item">
                                        <a href="layouts-horizontal.html" target="_blank" class="nav-link" data-key="t-horizontal">Horizontal</a>
                                    </li>
                                    <li class="nav-item">
                                        <a href="layouts-detached.html" target="_blank" class="nav-link" data-key="t-detached">Detached</a>
                                    </li>
                                    <li class="nav-item">
                                        <a href="layouts-two-column.html" target="_blank" class="nav-link" data-key="t-two-column">Two Column</a>
                                    </li>
                                    <li class="nav-item">
                                        <a href="layouts-vertical-hovered.html" target="_blank" class="nav-link" data-key="t-hovered">Hovered</a>
                                    </li>
                                </ul>
                            </div>
                        </li> <!-- end Dashboard Menu -->

                        <li class="menu-title"><i class="ri-more-fill"></i> <span data-key="t-pages">Pages</span></li>

                        <li class="nav-item">
                            <a class="nav-link menu-link" href="#sidebarAuth" data-bs-toggle="collapse" role="button" aria-expanded="false" aria-controls="sidebarAuth">
                                <i class="ri-account-circle-line"></i> <span data-key="t-authentication">Authentication</span>
                            </a>
                            <div class="collapse menu-dropdown" id="sidebarAuth">
                                <ul class="nav nav-sm flex-column">
                                    <li class="nav-item">
                                        <a href="#sidebarSignIn" class="nav-link" data-bs-toggle="collapse" role="button" aria-expanded="false" aria-controls="sidebarSignIn" data-key="t-signin"> Sign In
                                        </a>
                                        <div class="collapse menu-dropdown" id="sidebarSignIn">
                                            <ul class="nav nav-sm flex-column">
                                                <li class="nav-item">
                                                    <a href="auth-signin-basic.html" class="nav-link" data-key="t-basic"> Basic
                                                    </a>
                                                </li>
                                                <li class="nav-item">
                                                    <a href="auth-signin-cover.html" class="nav-link" data-key="t-cover"> Cover
                                                    </a>
                                                </li>
                                            </ul>
                                        </div>
                                    </li>
                                    <li class="nav-item">
                                        <a href="#sidebarSignUp" class="nav-link" data-bs-toggle="collapse" role="button" aria-expanded="false" aria-controls="sidebarSignUp" data-key="t-signup"> Sign Up
                                        </a>
                                        <div class="collapse menu-dropdown" id="sidebarSignUp">
                                            <ul class="nav nav-sm flex-column">
                                                <li class="nav-item">
                                                    <a href="auth-signup-basic.html" class="nav-link" data-key="t-basic"> Basic
                                                    </a>
                                                </li>
                                                <li class="nav-item">
                                                    <a href="auth-signup-cover.html" class="nav-link" data-key="t-cover"> Cover
                                                    </a>
                                                </li>
                                            </ul>
                                        </div>
                                    </li>

                                    <li class="nav-item">
                                        <a href="#sidebarResetPass" class="nav-link" data-bs-toggle="collapse" role="button" aria-expanded="false" aria-controls="sidebarResetPass" data-key="t-password-reset">
                                            Password Reset
                                        </a>
                                        <div class="collapse menu-dropdown" id="sidebarResetPass">
                                            <ul class="nav nav-sm flex-column">
                                                <li class="nav-item">
                                                    <a href="auth-pass-reset-basic.html" class="nav-link" data-key="t-basic">
                                                        Basic </a>
                                                </li>
                                                <li class="nav-item">
                                                    <a href="auth-pass-reset-cover.html" class="nav-link" data-key="t-cover">
                                                        Cover </a>
                                                </li>
                                            </ul>
                                        </div>
                                    </li>

                                    <li class="nav-item">
                                        <a href="#sidebarchangePass" class="nav-link" data-bs-toggle="collapse" role="button" aria-expanded="false" aria-controls="sidebarchangePass" data-key="t-password-create">
                                            Password Create
                                        </a>
                                        <div class="collapse menu-dropdown" id="sidebarchangePass">
                                            <ul class="nav nav-sm flex-column">
                                                <li class="nav-item">
                                                    <a href="auth-pass-change-basic.html" class="nav-link" data-key="t-basic">
                                                        Basic </a>
                                                </li>
                                                <li class="nav-item">
                                                    <a href="auth-pass-change-cover.html" class="nav-link" data-key="t-cover">
                                                        Cover </a>
                                                </li>
                                            </ul>
                                        </div>
                                    </li>

                                    <li class="nav-item">
                                        <a href="#sidebarLockScreen" class="nav-link" data-bs-toggle="collapse" role="button" aria-expanded="false" aria-controls="sidebarLockScreen" data-key="t-lock-screen">
                                            Lock Screen
                                        </a>
                                        <div class="collapse menu-dropdown" id="sidebarLockScreen">
                                            <ul class="nav nav-sm flex-column">
                                                <li class="nav-item">
                                                    <a href="auth-lockscreen-basic.html" class="nav-link" data-key="t-basic">
                                                        Basic </a>
                                                </li>
                                                <li class="nav-item">
                                                    <a href="auth-lockscreen-cover.html" class="nav-link" data-key="t-cover">
                                                        Cover </a>
                                                </li>
                                            </ul>
                                        </div>
                                    </li>

                                    <li class="nav-item">
                                        <a href="#sidebarLogout" class="nav-link" data-bs-toggle="collapse" role="button" aria-expanded="false" aria-controls="sidebarLogout" data-key="t-logout"> Logout
                                        </a>
                                        <div class="collapse menu-dropdown" id="sidebarLogout">
                                            <ul class="nav nav-sm flex-column">
                                                <li class="nav-item">
                                                    <a href="auth-logout-basic.html" class="nav-link" data-key="t-basic"> Basic
                                                    </a>
                                                </li>
                                                <li class="nav-item">
                                                    <a href="auth-logout-cover.html" class="nav-link" data-key="t-cover"> Cover
                                                    </a>
                                                </li>
                                            </ul>
                                        </div>
                                    </li>
                                    <li class="nav-item">
                                        <a href="#sidebarSuccessMsg" class="nav-link" data-bs-toggle="collapse" role="button" aria-expanded="false" aria-controls="sidebarSuccessMsg" data-key="t-success-message"> Success Message
                                        </a>
                                        <div class="collapse menu-dropdown" id="sidebarSuccessMsg">
                                            <ul class="nav nav-sm flex-column">
                                                <li class="nav-item">
                                                    <a href="auth-success-msg-basic.html" class="nav-link" data-key="t-basic">
                                                        Basic </a>
                                                </li>
                                                <li class="nav-item">
                                                    <a href="auth-success-msg-cover.html" class="nav-link" data-key="t-cover">
                                                        Cover </a>
                                                </li>
                                            </ul>
                                        </div>
                                    </li>
                                    <li class="nav-item">
                                        <a href="#sidebarTwoStep" class="nav-link" data-bs-toggle="collapse" role="button" aria-expanded="false" aria-controls="sidebarTwoStep" data-key="t-two-step-verification"> Two Step Verification
                                        </a>
                                        <div class="collapse menu-dropdown" id="sidebarTwoStep">
                                            <ul class="nav nav-sm flex-column">
                                                <li class="nav-item">
                                                    <a href="auth-twostep-basic.html" class="nav-link" data-key="t-basic"> Basic
                                                    </a>
                                                </li>
                                                <li class="nav-item">
                                                    <a href="auth-twostep-cover.html" class="nav-link" data-key="t-cover"> Cover
                                                    </a>
                                                </li>
                                            </ul>
                                        </div>
                                    </li>
                                    <li class="nav-item">
                                        <a href="#sidebarErrors" class="nav-link" data-bs-toggle="collapse" role="button" aria-expanded="false" aria-controls="sidebarErrors" data-key="t-errors"> Errors
                                        </a>
                                        <div class="collapse menu-dropdown" id="sidebarErrors">
                                            <ul class="nav nav-sm flex-column">
                                                <li class="nav-item">
                                                    <a href="auth-404-basic.html" class="nav-link" data-key="t-404-basic"> 404
                                                        Basic </a>
                                                </li>
                                                <li class="nav-item">
                                                    <a href="auth-404-cover.html" class="nav-link" data-key="t-404-cover"> 404
                                                        Cover </a>
                                                </li>
                                                <li class="nav-item">
                                                    <a href="auth-404-alt.html" class="nav-link" data-key="t-404-alt"> 404 Alt
                                                    </a>
                                                </li>
                                                <li class="nav-item">
                                                    <a href="auth-500.html" class="nav-link" data-key="t-500"> 500 </a>
                                                </li>
                                                <li class="nav-item">
                                                    <a href="auth-offline.html" class="nav-link" data-key="t-offline-page"> Offline Page </a>
                                                </li>
                                            </ul>
                                        </div>
                                    </li>
                                </ul>
                            </div>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link menu-link" href="#sidebarPages" data-bs-toggle="collapse" role="button" aria-expanded="false" aria-controls="sidebarPages">
                                <i class="ri-pages-line"></i> <span data-key="t-pages">Pages</span>
                            </a>
                            <div class="collapse menu-dropdown" id="sidebarPages">
                                <ul class="nav nav-sm flex-column">
                                    <li class="nav-item">
                                        <a href="pages-starter.html" class="nav-link" data-key="t-starter"> Starter </a>
                                    </li>
                                    <li class="nav-item">
                                        <a href="#sidebarProfile" class="nav-link" data-bs-toggle="collapse" role="button" aria-expanded="false" aria-controls="sidebarProfile" data-key="t-profile"> Profile
                                        </a>
                                        <div class="collapse menu-dropdown" id="sidebarProfile">
                                            <ul class="nav nav-sm flex-column">
                                                <li class="nav-item">
                                                    <a href="pages-profile.html" class="nav-link" data-key="t-simple-page">
                                                        Simple Page </a>
                                                </li>
                                                <li class="nav-item">
                                                    <a href="pages-profile-settings.html" class="nav-link" data-key="t-settings"> Settings </a>
                                                </li>
                                            </ul>
                                        </div>
                                    </li>
                                    <li class="nav-item">
                                        <a href="pages-team.html" class="nav-link" data-key="t-team"> Team </a>
                                    </li>
                                    <li class="nav-item">
                                        <a href="pages-timeline.html" class="nav-link" data-key="t-timeline"> Timeline </a>
                                    </li>
                                    <li class="nav-item">
                                        <a href="pages-faqs.html" class="nav-link" data-key="t-faqs"> FAQs </a>
                                    </li>
                                    <li class="nav-item">
                                        <a href="pages-pricing.html" class="nav-link" data-key="t-pricing"> Pricing </a>
                                    </li>
                                    <li class="nav-item">
                                        <a href="pages-gallery.html" class="nav-link" data-key="t-gallery"> Gallery </a>
                                    </li>
                                    <li class="nav-item">
                                        <a href="pages-maintenance.html" class="nav-link" data-key="t-maintenance"> Maintenance
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a href="pages-coming-soon.html" class="nav-link" data-key="t-coming-soon"> Coming Soon
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a href="pages-sitemap.html" class="nav-link" data-key="t-sitemap"> Sitemap </a>
                                    </li>
                                    <li class="nav-item">
                                        <a href="pages-search-results.html" class="nav-link" data-key="t-search-results"> Search Results </a>
                                    </li>
                                    <li class="nav-item">
                                        <a href="pages-privacy-policy.html" class="nav-link" data-key="t-privacy-policy">Privacy Policy</a>
                                    </li>
                                    <li class="nav-item">
                                        <a href="pages-term-conditions.html" class="nav-link" data-key="t-term-conditions">Term & Conditions</a>
                                    </li>
                                </ul>
                            </div>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link menu-link" href="#sidebarLanding" data-bs-toggle="collapse" role="button" aria-expanded="false" aria-controls="sidebarLanding">
                                <i class="ri-rocket-line"></i> <span data-key="t-landing">Landing</span>
                            </a>
                            <div class="collapse menu-dropdown" id="sidebarLanding">
                                <ul class="nav nav-sm flex-column">
                                    <li class="nav-item">
                                        <a href="landing.html" class="nav-link" data-key="t-one-page"> One Page </a>
                                    </li>
                                    <li class="nav-item">
                                        <a href="nft-landing.html" class="nav-link" data-key="t-nft-landing"> NFT Landing </a>
                                    </li>
                                    <li class="nav-item">
                                        <a href="job-landing.html" class="nav-link" data-key="t-job">Job</a>
                                    </li>
                                </ul>
                            </div>
                        </li>

                        <li class="menu-title"><i class="ri-more-fill"></i> <span data-key="t-components">Components</span></li>

                        <li class="nav-item">
                            <a class="nav-link menu-link" href="#sidebarUI" data-bs-toggle="collapse" role="button" aria-expanded="false" aria-controls="sidebarUI">
                                <i class="ri-pencil-ruler-2-line"></i> <span data-key="t-base-ui">Base UI</span>
                            </a>
                            <div class="collapse menu-dropdown mega-dropdown-menu" id="sidebarUI">
                                <div class="row">
                                    <div class="col-lg-4">
                                        <ul class="nav nav-sm flex-column">
                                            <li class="nav-item">
                                                <a href="ui-alerts.html" class="nav-link" data-key="t-alerts">Alerts</a>
                                            </li>
                                            <li class="nav-item">
                                                <a href="ui-badges.html" class="nav-link" data-key="t-badges">Badges</a>
                                            </li>
                                            <li class="nav-item">
                                                <a href="ui-buttons.html" class="nav-link" data-key="t-buttons">Buttons</a>
                                            </li>
                                            <li class="nav-item">
                                                <a href="ui-colors.html" class="nav-link" data-key="t-colors">Colors</a>
                                            </li>
                                            <li class="nav-item">
                                                <a href="ui-cards.html" class="nav-link" data-key="t-cards">Cards</a>
                                            </li>
                                            <li class="nav-item">
                                                <a href="ui-carousel.html" class="nav-link" data-key="t-carousel">Carousel</a>
                                            </li>
                                            <li class="nav-item">
                                                <a href="ui-dropdowns.html" class="nav-link" data-key="t-dropdowns">Dropdowns</a>
                                            </li>
                                            <li class="nav-item">
                                                <a href="ui-grid.html" class="nav-link" data-key="t-grid">Grid</a>
                                            </li>
                                        </ul>
                                    </div>
                                    <div class="col-lg-4">
                                        <ul class="nav nav-sm flex-column">
                                            <li class="nav-item">
                                                <a href="ui-images.html" class="nav-link" data-key="t-images">Images</a>
                                            </li>
                                            <li class="nav-item">
                                                <a href="ui-tabs.html" class="nav-link" data-key="t-tabs">Tabs</a>
                                            </li>
                                            <li class="nav-item">
                                                <a href="ui-accordions.html" class="nav-link" data-key="t-accordion-collapse">Accordion & Collapse</a>
                                            </li>
                                            <li class="nav-item">
                                                <a href="ui-modals.html" class="nav-link" data-key="t-modals">Modals</a>
                                            </li>
                                            <li class="nav-item">
                                                <a href="ui-offcanvas.html" class="nav-link" data-key="t-offcanvas">Offcanvas</a>
                                            </li>
                                            <li class="nav-item">
                                                <a href="ui-placeholders.html" class="nav-link" data-key="t-placeholders">Placeholders</a>
                                            </li>
                                            <li class="nav-item">
                                                <a href="ui-progress.html" class="nav-link" data-key="t-progress">Progress</a>
                                            </li>
                                            <li class="nav-item">
                                                <a href="ui-notifications.html" class="nav-link" data-key="t-notifications">Notifications</a>
                                            </li>
                                        </ul>
                                    </div>
                                    <div class="col-lg-4">
                                        <ul class="nav nav-sm flex-column">
                                            <li class="nav-item">
                                                <a href="ui-media.html" class="nav-link" data-key="t-media-object">Media
                                                    object</a>
                                            </li>
                                            <li class="nav-item">
                                                <a href="ui-embed-video.html" class="nav-link" data-key="t-embed-video">Embed
                                                    Video</a>
                                            </li>
                                            <li class="nav-item">
                                                <a href="ui-typography.html" class="nav-link" data-key="t-typography">Typography</a>
                                            </li>
                                            <li class="nav-item">
                                                <a href="ui-lists.html" class="nav-link" data-key="t-lists">Lists</a>
                                            </li>
                                            <li class="nav-item">
        										<a href="ui-links.html" class="nav-link"><span data-key="t-links">Links</span> <span class="badge badge-pill bg-success" data-key="t-new">New</span></a>
                                            </li>
                                            <li class="nav-item">
                                                <a href="ui-general.html" class="nav-link" data-key="t-general">General</a>
                                            </li>
                                            <li class="nav-item">
                                                <a href="ui-ribbons.html" class="nav-link" data-key="t-ribbons">Ribbons</a>
                                            </li>
                                            <li class="nav-item">
                                                <a href="ui-utilities.html" class="nav-link" data-key="t-utilities">Utilities</a>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link menu-link" href="#sidebarAdvanceUI" data-bs-toggle="collapse" role="button" aria-expanded="false" aria-controls="sidebarAdvanceUI">
                                <i class="ri-stack-line"></i> <span data-key="t-advance-ui">Advance UI</span>
                            </a>
                            <div class="collapse menu-dropdown" id="sidebarAdvanceUI">
                                <ul class="nav nav-sm flex-column">
                                    <li class="nav-item">
                                        <a href="advance-ui-sweetalerts.html" class="nav-link" data-key="t-sweet-alerts">Sweet
                                            Alerts</a>
                                    </li>
                                    <li class="nav-item">
                                        <a href="advance-ui-nestable.html" class="nav-link" data-key="t-nestable-list">Nestable
                                            List</a>
                                    </li>
                                    <li class="nav-item">
                                        <a href="advance-ui-scrollbar.html" class="nav-link" data-key="t-scrollbar">Scrollbar</a>
                                    </li>
                                    <li class="nav-item">
                                        <a href="advance-ui-animation.html" class="nav-link" data-key="t-animation">Animation</a>
                                    </li>
                                    <li class="nav-item">
                                        <a href="advance-ui-tour.html" class="nav-link" data-key="t-tour">Tour</a>
                                    </li>
                                    <li class="nav-item">
                                        <a href="advance-ui-swiper.html" class="nav-link" data-key="t-swiper-slider">Swiper
                                            Slider</a>
                                    </li>
                                    <li class="nav-item">
                                        <a href="advance-ui-ratings.html" class="nav-link" data-key="t-ratings">Ratings</a>
                                    </li>
                                    <li class="nav-item">
                                        <a href="advance-ui-highlight.html" class="nav-link" data-key="t-highlight">Highlight</a>
                                    </li>
                                    <li class="nav-item">
                                        <a href="advance-ui-scrollspy.html" class="nav-link" data-key="t-scrollSpy">ScrollSpy</a>
                                    </li>
                                </ul>
                            </div>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link menu-link" href="widgets.html">
                                <i class="ri-honour-line"></i> <span data-key="t-widgets">Widgets</span>
                            </a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link menu-link" href="#sidebarForms" data-bs-toggle="collapse" role="button" aria-expanded="false" aria-controls="sidebarForms">
                                <i class="ri-file-list-3-line"></i> <span data-key="t-forms">Forms</span>
                            </a>
                            <div class="collapse menu-dropdown" id="sidebarForms">
                                <ul class="nav nav-sm flex-column">
                                    <li class="nav-item">
                                        <a href="forms-elements.html" class="nav-link" data-key="t-basic-elements">Basic
                                            Elements</a>
                                    </li>
                                    <li class="nav-item">
                                        <a href="forms-select.html" class="nav-link" data-key="t-form-select"> Form Select </a>
                                    </li>
                                    <li class="nav-item">
                                        <a href="forms-checkboxs-radios.html" class="nav-link" data-key="t-checkboxs-radios">Checkboxs & Radios</a>
                                    </li>
                                    <li class="nav-item">
                                        <a href="forms-pickers.html" class="nav-link" data-key="t-pickers"> Pickers </a>
                                    </li>
                                    <li class="nav-item">
                                        <a href="forms-masks.html" class="nav-link" data-key="t-input-masks">Input Masks</a>
                                    </li>
                                    <li class="nav-item">
                                        <a href="forms-advanced.html" class="nav-link" data-key="t-advanced">Advanced</a>
                                    </li>
                                    <li class="nav-item">
                                        <a href="forms-range-sliders.html" class="nav-link" data-key="t-range-slider"> Range
                                            Slider </a>
                                    </li>
                                    <li class="nav-item">
                                        <a href="forms-validation.html" class="nav-link" data-key="t-validation">Validation</a>
                                    </li>
                                    <li class="nav-item">
                                        <a href="forms-wizard.html" class="nav-link" data-key="t-wizard">Wizard</a>
                                    </li>
                                    <li class="nav-item">
                                        <a href="forms-editors.html" class="nav-link" data-key="t-editors">Editors</a>
                                    </li>
                                    <li class="nav-item">
                                        <a href="forms-file-uploads.html" class="nav-link" data-key="t-file-uploads">File
                                            Uploads</a>
                                    </li>
                                    <li class="nav-item">
                                        <a href="forms-layouts.html" class="nav-link" data-key="t-form-layouts">Form Layouts</a>
                                    </li>
                                    <li class="nav-item">
                                        <a href="forms-select2.html" class="nav-link" data-key="t-select2">Select2</a>
                                    </li>
                                </ul>
                            </div>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link menu-link" href="#sidebarTables" data-bs-toggle="collapse" role="button" aria-expanded="false" aria-controls="sidebarTables">
                                <i class="ri-layout-grid-line"></i> <span data-key="t-tables">Tables</span>
                            </a>
                            <div class="collapse menu-dropdown" id="sidebarTables">
                                <ul class="nav nav-sm flex-column">
                                    <li class="nav-item">
                                        <a href="tables-basic.html" class="nav-link" data-key="t-basic-tables">Basic Tables</a>
                                    </li>
                                    <li class="nav-item">
                                        <a href="tables-gridjs.html" class="nav-link" data-key="t-grid-js">Grid Js</a>
                                    </li>
                                    <li class="nav-item">
                                        <a href="tables-listjs.html" class="nav-link" data-key="t-list-js">List Js</a>
                                    </li>
                                    <li class="nav-item">
                                        <a href="tables-datatables.html" class="nav-link" data-key="t-datatables">Datatables</a>
                                    </li>
                                </ul>
                            </div>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link menu-link" href="#sidebarCharts" data-bs-toggle="collapse" role="button" aria-expanded="false" aria-controls="sidebarCharts">
                                <i class="ri-pie-chart-line"></i> <span data-key="t-charts">Charts</span>
                            </a>
                            <div class="collapse menu-dropdown" id="sidebarCharts">
                                <ul class="nav nav-sm flex-column">
                                    <li class="nav-item">
                                        <a href="#sidebarApexcharts" class="nav-link" data-bs-toggle="collapse" role="button" aria-expanded="false" aria-controls="sidebarApexcharts" data-key="t-apexcharts">
                                            Apexcharts
                                        </a>
                                        <div class="collapse menu-dropdown" id="sidebarApexcharts">
                                            <ul class="nav nav-sm flex-column">
                                                <li class="nav-item">
                                                    <a href="charts-apex-line.html" class="nav-link" data-key="t-line"> Line
                                                    </a>
                                                </li>
                                                <li class="nav-item">
                                                    <a href="charts-apex-area.html" class="nav-link" data-key="t-area"> Area
                                                    </a>
                                                </li>
                                                <li class="nav-item">
                                                    <a href="charts-apex-column.html" class="nav-link" data-key="t-column">
                                                        Column </a>
                                                </li>
                                                <li class="nav-item">
                                                    <a href="charts-apex-bar.html" class="nav-link" data-key="t-bar"> Bar </a>
                                                </li>
                                                <li class="nav-item">
                                                    <a href="charts-apex-mixed.html" class="nav-link" data-key="t-mixed"> Mixed
                                                    </a>
                                                </li>
                                                <li class="nav-item">
                                                    <a href="charts-apex-timeline.html" class="nav-link" data-key="t-timeline">
                                                        Timeline </a>
                                                </li>
                                                <li class="nav-item">
                                                    <a href="charts-apex-range-area.html" class="nav-link"><span data-key="t-range-area">Range Area</span> <span class="badge badge-pill bg-success" data-key="t-new">New</span></a>
                                                </li>
                                                <li class="nav-item">
                                                    <a href="charts-apex-funnel.html" class="nav-link"><span data-key="t-funnel">Funnel</span> <span class="badge badge-pill bg-success" data-key="t-new">New</span></a>
                                                </li>
                                                <li class="nav-item">
                                                    <a href="charts-apex-candlestick.html" class="nav-link" data-key="t-candlstick"> Candlstick </a>
                                                </li>
                                                <li class="nav-item">
                                                    <a href="charts-apex-boxplot.html" class="nav-link" data-key="t-boxplot">
                                                        Boxplot </a>
                                                </li>
                                                <li class="nav-item">
                                                    <a href="charts-apex-bubble.html" class="nav-link" data-key="t-bubble">
                                                        Bubble </a>
                                                </li>
                                                <li class="nav-item">
                                                    <a href="charts-apex-scatter.html" class="nav-link" data-key="t-scatter">
                                                        Scatter </a>
                                                </li>
                                                <li class="nav-item">
                                                    <a href="charts-apex-heatmap.html" class="nav-link" data-key="t-heatmap">
                                                        Heatmap </a>
                                                </li>
                                                <li class="nav-item">
                                                    <a href="charts-apex-treemap.html" class="nav-link" data-key="t-treemap">
                                                        Treemap </a>
                                                </li>
                                                <li class="nav-item">
                                                    <a href="charts-apex-pie.html" class="nav-link" data-key="t-pie"> Pie </a>
                                                </li>
                                                <li class="nav-item">
                                                    <a href="charts-apex-radialbar.html" class="nav-link" data-key="t-radialbar"> Radialbar </a>
                                                </li>
                                                <li class="nav-item">
                                                    <a href="charts-apex-radar.html" class="nav-link" data-key="t-radar"> Radar
                                                    </a>
                                                </li>
                                                <li class="nav-item">
                                                    <a href="charts-apex-polar.html" class="nav-link" data-key="t-polar-area">
                                                        Polar Area </a>
                                                </li>
                                            </ul>
                                        </div>
                                    </li>
                                    <li class="nav-item">
                                        <a href="charts-chartjs.html" class="nav-link" data-key="t-chartjs"> Chartjs </a>
                                    </li>
                                    <li class="nav-item">
                                        <a href="charts-echarts.html" class="nav-link" data-key="t-echarts"> Echarts </a>
                                    </li>
                                </ul>
                            </div>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link menu-link" href="#sidebarIcons" data-bs-toggle="collapse" role="button" aria-expanded="false" aria-controls="sidebarIcons">
                                <i class="ri-compasses-2-line"></i> <span data-key="t-icons">Icons</span>
                            </a>
                            <div class="collapse menu-dropdown" id="sidebarIcons">
                                <ul class="nav nav-sm flex-column">
                                    <li class="nav-item">
                                        <a href="icons-remix.html" class="nav-link"><span data-key="t-remix">Remix</span> <span class="badge badge-pill bg-info">v3.6</span></a>
                                    </li>
                                    <li class="nav-item">
                                        <a href="icons-boxicons.html" class="nav-link"><span data-key="t-boxicons">Boxicons</span> <span class="badge badge-pill bg-info">v2.1.4</span></a>
                                    </li>
                                    <li class="nav-item">
                                        <a href="icons-materialdesign.html" class="nav-link"><span data-key="t-material-design">Material Design</span> <span class="badge badge-pill bg-info">v7.2.96</span></a>
                                    </li>
                                    <li class="nav-item">
                                        <a href="icons-lineawesome.html" class="nav-link" data-key="t-line-awesome">Line Awesome</a>
                                    </li>
                                    <li class="nav-item">
                                        <a href="icons-feather.html" class="nav-link"><span data-key="t-feather">Feather</span> <span class="badge badge-pill bg-info">v4.29.1</span></a>
                                    </li>
                                    <li class="nav-item">
                                        <a href="icons-crypto.html" class="nav-link"> <span data-key="t-crypto-svg">Crypto SVG</span></a>
                                    </li>
                                </ul>
                            </div>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link menu-link" href="#sidebarMaps" data-bs-toggle="collapse" role="button" aria-expanded="false" aria-controls="sidebarMaps">
                                <i class="ri-map-pin-line"></i> <span data-key="t-maps">Maps</span>
                            </a>
                            <div class="collapse menu-dropdown" id="sidebarMaps">
                                <ul class="nav nav-sm flex-column">
                                    <li class="nav-item">
                                        <a href="maps-google.html" class="nav-link" data-key="t-google">
                                            Google
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a href="maps-vector.html" class="nav-link" data-key="t-vector">
                                            Vector
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a href="maps-leaflet.html" class="nav-link" data-key="t-leaflet">
                                            Leaflet
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link menu-link" href="#sidebarMultilevel" data-bs-toggle="collapse" role="button" aria-expanded="false" aria-controls="sidebarMultilevel">
                                <i class="ri-share-line"></i> <span data-key="t-multi-level">Multi Level</span>
                            </a>
                            <div class="collapse menu-dropdown" id="sidebarMultilevel">
                                <ul class="nav nav-sm flex-column">
                                    <li class="nav-item">
                                        <a href="#" class="nav-link" data-key="t-level-1.1"> Level 1.1 </a>
                                    </li>
                                    <li class="nav-item">
                                        <a href="#sidebarAccount" class="nav-link" data-bs-toggle="collapse" role="button" aria-expanded="false" aria-controls="sidebarAccount" data-key="t-level-1.2"> Level
                                            1.2
                                        </a>
                                        <div class="collapse menu-dropdown" id="sidebarAccount">
                                            <ul class="nav nav-sm flex-column">
                                                <li class="nav-item">
                                                    <a href="#" class="nav-link" data-key="t-level-2.1"> Level 2.1 </a>
                                                </li>
                                                <li class="nav-item">
                                                    <a href="#sidebarCrm" class="nav-link" data-bs-toggle="collapse" role="button" aria-expanded="false" aria-controls="sidebarCrm" data-key="t-level-2.2"> Level 2.2
                                                    </a>
                                                    <div class="collapse menu-dropdown" id="sidebarCrm">
                                                        <ul class="nav nav-sm flex-column">
                                                            <li class="nav-item">
                                                                <a href="#" class="nav-link" data-key="t-level-3.1"> Level 3.1
                                                                </a>
                                                            </li>
                                                            <li class="nav-item">
                                                                <a href="#" class="nav-link" data-key="t-level-3.2"> Level 3.2
                                                                </a>
                                                            </li>
                                                        </ul>
                                                    </div>
                                                </li>
                                            </ul>
                                        </div>
                                    </li>
                                </ul>
                            </div>
                        </li>

                    </ul>
                </div>
                <!-- Sidebar -->
            </div>

            <div class="sidebar-background"></div>
        </div>
        <!-- Left Sidebar End -->
        <!-- Vertical Overlay-->
        <div class="vertical-overlay"></div>

        <!-- ============================================================== -->
        <!-- Start right Content here -->
        <!-- ============================================================== -->
        <div class="main-content">

            <div class="page-content">
                <div class="container-fluid">

                    <!-- start page title -->
                    <div class="row">
                        <div class="col-12">
                            <div class="page-title-box d-sm-flex align-items-center justify-content-between bg-galaxy-transparent">
                                <h4 class="mb-sm-0">Crypto Icons</h4>

                                <div class="page-title-right">
                                    <ol class="breadcrumb m-0">
                                        <li class="breadcrumb-item"><a href="javascript: void(0);">Pages</a></li>
                                        <li class="breadcrumb-item active">Crypto Icons</li>
                                    </ol>
                                </div>

                            </div>
                        </div>
                    </div>
                    <!-- end page title -->

                    <div class="row">
                        <div class="col-lg-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-1">Examples</h5>
                                    <p class="text-muted mb-0">Use <code>&lt;img src="assets/images/svg/crypto-icons/**.svg"&gt;</code> class.</p>
                                </div>
                                <div class="card-body">
                                    <div class="row icon-demo-content">
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/%24pac.svg" alt="" class="avatar-xxs" /> $pac
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/0xbtc.svg" alt="" class="avatar-xxs" /> 0xbtc
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/2give.svg" alt="" class="avatar-xxs" /> 2give
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/aave.svg" alt="" class="avatar-xxs" /> aave
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/abt.svg" alt="" class="avatar-xxs" /> abt
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/act.svg" alt="" class="avatar-xxs" /> act
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/actn.svg" alt="" class="avatar-xxs" /> actn
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/ada.svg" alt="" class="avatar-xxs" /> ada
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/add.svg" alt="" class="avatar-xxs" /> add
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/adx.svg" alt="" class="avatar-xxs" /> adx
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/ae.svg" alt="" class="avatar-xxs" /> ae
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/aeon.svg" alt="" class="avatar-xxs" /> aeon
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/aeur.svg" alt="" class="avatar-xxs" /> aeur
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/agi.svg" alt="" class="avatar-xxs" /> agi
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/agrs.svg" alt="" class="avatar-xxs" /> agrs
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/aion.svg" alt="" class="avatar-xxs" /> aion
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/algo.svg" alt="" class="avatar-xxs" /> algo
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/amb.svg" alt="" class="avatar-xxs" /> amb
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/amp.svg" alt="" class="avatar-xxs" /> amp
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/ampl.svg" alt="" class="avatar-xxs" /> ampl
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/ankr.svg" alt="" class="avatar-xxs" /> ankr
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/ant.svg" alt="" class="avatar-xxs" /> ant
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/apex.svg" alt="" class="avatar-xxs" /> apex
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/appc.svg" alt="" class="avatar-xxs" /> appc
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/ardr.svg" alt="" class="avatar-xxs" /> ardr
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/arg.svg" alt="" class="avatar-xxs" /> arg
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/ark.svg" alt="" class="avatar-xxs" /> ark
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/arn.svg" alt="" class="avatar-xxs" /> arn
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/arnx.svg" alt="" class="avatar-xxs" /> arnx
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/ary.svg" alt="" class="avatar-xxs" /> ary
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/ast.svg" alt="" class="avatar-xxs" /> ast
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/atm.svg" alt="" class="avatar-xxs" /> atm
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/atom.svg" alt="" class="avatar-xxs" /> atom
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/audr.svg" alt="" class="avatar-xxs" /> audr
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/auto.svg" alt="" class="avatar-xxs" /> auto
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/aywa.svg" alt="" class="avatar-xxs" /> aywa
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/bab.svg" alt="" class="avatar-xxs" /> bab
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/bal.svg" alt="" class="avatar-xxs" /> bal
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/band.svg" alt="" class="avatar-xxs" /> band
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/bat.svg" alt="" class="avatar-xxs" /> bat
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/bay.svg" alt="" class="avatar-xxs" /> bay
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/bcbc.svg" alt="" class="avatar-xxs" /> bcbc
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/bcc.svg" alt="" class="avatar-xxs" /> bcc
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/bcd.svg" alt="" class="avatar-xxs" /> bcd
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/bch.svg" alt="" class="avatar-xxs" /> bch
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/bcio.svg" alt="" class="avatar-xxs" /> bcio
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/bcn.svg" alt="" class="avatar-xxs" /> bcn
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/bco.svg" alt="" class="avatar-xxs" /> bco
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/bcpt.svg" alt="" class="avatar-xxs" /> bcpt
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/bdl.svg" alt="" class="avatar-xxs" /> bdl
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/beam.svg" alt="" class="avatar-xxs" /> beam
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/bela.svg" alt="" class="avatar-xxs" /> bela
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/bix.svg" alt="" class="avatar-xxs" /> bix
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/blcn.svg" alt="" class="avatar-xxs" /> blcn
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/blk.svg" alt="" class="avatar-xxs" /> blk
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/block.svg" alt="" class="avatar-xxs" /> block
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/blz.svg" alt="" class="avatar-xxs" /> blz
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/bnb.svg" alt="" class="avatar-xxs" /> bnb
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/bnt.svg" alt="" class="avatar-xxs" /> bnt
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/bnty.svg" alt="" class="avatar-xxs" /> bnty
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/booty.svg" alt="" class="avatar-xxs" /> booty
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/bos.svg" alt="" class="avatar-xxs" /> bos
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/bpt.svg" alt="" class="avatar-xxs" /> bpt
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/bq.svg" alt="" class="avatar-xxs" /> bq
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/brd.svg" alt="" class="avatar-xxs" /> brd
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/bsd.svg" alt="" class="avatar-xxs" /> bsd
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/bsv.svg" alt="" class="avatar-xxs" /> bsv
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/btc.svg" alt="" class="avatar-xxs" /> btc
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/btcd.svg" alt="" class="avatar-xxs" /> btcd
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/btch.svg" alt="" class="avatar-xxs" /> btch
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/btcp.svg" alt="" class="avatar-xxs" /> btcp
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/btcz.svg" alt="" class="avatar-xxs" /> btcz
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/btdx.svg" alt="" class="avatar-xxs" /> btdx
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/btg.svg" alt="" class="avatar-xxs" /> btg
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/btm.svg" alt="" class="avatar-xxs" /> btm
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/bts.svg" alt="" class="avatar-xxs" /> bts
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/btt.svg" alt="" class="avatar-xxs" /> btt
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/btx.svg" alt="" class="avatar-xxs" /> btx
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/burst.svg" alt="" class="avatar-xxs" /> burst
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/bze.svg" alt="" class="avatar-xxs" /> bze
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/call.svg" alt="" class="avatar-xxs" /> call
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/cc.svg" alt="" class="avatar-xxs" /> cc
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/cdn.svg" alt="" class="avatar-xxs" /> cdn
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/cdt.svg" alt="" class="avatar-xxs" /> cdt
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/cenz.svg" alt="" class="avatar-xxs" /> cenz
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/chain.svg" alt="" class="avatar-xxs" /> chain
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/chat.svg" alt="" class="avatar-xxs" /> chat
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/chips.svg" alt="" class="avatar-xxs" /> chips
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/chsb.svg" alt="" class="avatar-xxs" /> chsb
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/cix.svg" alt="" class="avatar-xxs" /> cix
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/clam.svg" alt="" class="avatar-xxs" /> clam
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/cloak.svg" alt="" class="avatar-xxs" /> cloak
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/cmm.svg" alt="" class="avatar-xxs" /> cmm
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/cmt.svg" alt="" class="avatar-xxs" /> cmt
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/cnd.svg" alt="" class="avatar-xxs" /> cnd
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/cnx.svg" alt="" class="avatar-xxs" /> cnx
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/cny.svg" alt="" class="avatar-xxs" /> cny
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/cob.svg" alt="" class="avatar-xxs" /> cob
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/colx.svg" alt="" class="avatar-xxs" /> colx
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/comp.svg" alt="" class="avatar-xxs" /> comp
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/coqui.svg" alt="" class="avatar-xxs" /> coqui
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/cred.svg" alt="" class="avatar-xxs" /> cred
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/crpt.svg" alt="" class="avatar-xxs" /> crpt
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/crv.svg" alt="" class="avatar-xxs" /> crv
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/crw.svg" alt="" class="avatar-xxs" /> crw
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/cs.svg" alt="" class="avatar-xxs" /> cs
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/ctr.svg" alt="" class="avatar-xxs" /> ctr
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/ctxc.svg" alt="" class="avatar-xxs" /> ctxc
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/cvc.svg" alt="" class="avatar-xxs" /> cvc
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/d.svg" alt="" class="avatar-xxs" /> d
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/dai.svg" alt="" class="avatar-xxs" /> dai
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/dash.svg" alt="" class="avatar-xxs" /> dash
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/dat.svg" alt="" class="avatar-xxs" /> dat
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/data.svg" alt="" class="avatar-xxs" /> data
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/dbc.svg" alt="" class="avatar-xxs" /> dbc
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/dcn.svg" alt="" class="avatar-xxs" /> dcn
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/dcr.svg" alt="" class="avatar-xxs" /> dcr
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/deez.svg" alt="" class="avatar-xxs" /> deez
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/dent.svg" alt="" class="avatar-xxs" /> dent
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/dew.svg" alt="" class="avatar-xxs" /> dew
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/dgb.svg" alt="" class="avatar-xxs" /> dgb
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/dgd.svg" alt="" class="avatar-xxs" /> dgd
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/dlt.svg" alt="" class="avatar-xxs" /> dlt
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/dnt.svg" alt="" class="avatar-xxs" /> dnt
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/dock.svg" alt="" class="avatar-xxs" /> dock
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/doge.svg" alt="" class="avatar-xxs" /> doge
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/dot.svg" alt="" class="avatar-xxs" /> dot
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/drgn.svg" alt="" class="avatar-xxs" /> drgn
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/drop.svg" alt="" class="avatar-xxs" /> drop
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/dta.svg" alt="" class="avatar-xxs" /> dta
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/dth.svg" alt="" class="avatar-xxs" /> dth
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/dtr.svg" alt="" class="avatar-xxs" /> dtr
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/ebst.svg" alt="" class="avatar-xxs" /> ebst
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/eca.svg" alt="" class="avatar-xxs" /> eca
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/edg.svg" alt="" class="avatar-xxs" /> edg
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/edo.svg" alt="" class="avatar-xxs" /> edo
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/edoge.svg" alt="" class="avatar-xxs" /> edoge
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/ela.svg" alt="" class="avatar-xxs" /> ela
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/elec.svg" alt="" class="avatar-xxs" /> elec
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/elf.svg" alt="" class="avatar-xxs" /> elf
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/elix.svg" alt="" class="avatar-xxs" /> elix
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/ella.svg" alt="" class="avatar-xxs" /> ella
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/emb.svg" alt="" class="avatar-xxs" /> emb
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/emc.svg" alt="" class="avatar-xxs" /> emc
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/emc2.svg" alt="" class="avatar-xxs" /> emc2
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/eng.svg" alt="" class="avatar-xxs" /> eng
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/enj.svg" alt="" class="avatar-xxs" /> enj
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/entrp.svg" alt="" class="avatar-xxs" /> entrp
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/eon.svg" alt="" class="avatar-xxs" /> eon
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/eop.svg" alt="" class="avatar-xxs" /> eop
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/eos.svg" alt="" class="avatar-xxs" /> eos
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/eqli.svg" alt="" class="avatar-xxs" /> eqli
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/equa.svg" alt="" class="avatar-xxs" /> equa
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/etc.svg" alt="" class="avatar-xxs" /> etc
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/eth.svg" alt="" class="avatar-xxs" /> eth
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/ethos.svg" alt="" class="avatar-xxs" /> ethos
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/etn.svg" alt="" class="avatar-xxs" /> etn
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/etp.svg" alt="" class="avatar-xxs" /> etp
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/eur.svg" alt="" class="avatar-xxs" /> eur
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/evx.svg" alt="" class="avatar-xxs" /> evx
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/exmo.svg" alt="" class="avatar-xxs" /> exmo
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/exp.svg" alt="" class="avatar-xxs" /> exp
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/fair.svg" alt="" class="avatar-xxs" /> fair
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/fct.svg" alt="" class="avatar-xxs" /> fct
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/fil.svg" alt="" class="avatar-xxs" /> fil
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/fjc.svg" alt="" class="avatar-xxs" /> fjc
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/fldc.svg" alt="" class="avatar-xxs" /> fldc
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/flo.svg" alt="" class="avatar-xxs" /> flo
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/flux.svg" alt="" class="avatar-xxs" /> flux
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/fsn.svg" alt="" class="avatar-xxs" /> fsn
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/ftc.svg" alt="" class="avatar-xxs" /> ftc
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/fuel.svg" alt="" class="avatar-xxs" /> fuel
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/fun.svg" alt="" class="avatar-xxs" /> fun
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/game.svg" alt="" class="avatar-xxs" /> game
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/gas.svg" alt="" class="avatar-xxs" /> gas
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/gbp.svg" alt="" class="avatar-xxs" /> gbp
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/gbx.svg" alt="" class="avatar-xxs" /> gbx
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/gbyte.svg" alt="" class="avatar-xxs" /> gbyte
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/generic.svg" alt="" class="avatar-xxs" /> generic
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/gin.svg" alt="" class="avatar-xxs" /> gin
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/glxt.svg" alt="" class="avatar-xxs" /> glxt
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/gmr.svg" alt="" class="avatar-xxs" /> gmr
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/gno.svg" alt="" class="avatar-xxs" /> gno
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/gnt.svg" alt="" class="avatar-xxs" /> gnt
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/gold.svg" alt="" class="avatar-xxs" /> gold
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/grc.svg" alt="" class="avatar-xxs" /> grc
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/grin.svg" alt="" class="avatar-xxs" /> grin
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/grs.svg" alt="" class="avatar-xxs" /> grs
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/grt.svg" alt="" class="avatar-xxs" /> grt
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/gsc.svg" alt="" class="avatar-xxs" /> gsc
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/gto.svg" alt="" class="avatar-xxs" /> gto
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/gup.svg" alt="" class="avatar-xxs" /> gup
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/gusd.svg" alt="" class="avatar-xxs" /> gusd
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/gvt.svg" alt="" class="avatar-xxs" /> gvt
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/gxs.svg" alt="" class="avatar-xxs" /> gxs
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/gzr.svg" alt="" class="avatar-xxs" /> gzr
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/hight.svg" alt="" class="avatar-xxs" /> hight
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/hns.svg" alt="" class="avatar-xxs" /> hns
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/hodl.svg" alt="" class="avatar-xxs" /> hodl
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/hot.svg" alt="" class="avatar-xxs" /> hot
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/hpb.svg" alt="" class="avatar-xxs" /> hpb
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/hsr.svg" alt="" class="avatar-xxs" /> hsr
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/ht.svg" alt="" class="avatar-xxs" /> ht
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/html.svg" alt="" class="avatar-xxs" /> html
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/huc.svg" alt="" class="avatar-xxs" /> huc
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/husd.svg" alt="" class="avatar-xxs" /> husd
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/hush.svg" alt="" class="avatar-xxs" /> hush
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/icn.svg" alt="" class="avatar-xxs" /> icn
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/icp.svg" alt="" class="avatar-xxs" /> icp
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/icx.svg" alt="" class="avatar-xxs" /> icx
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/ignis.svg" alt="" class="avatar-xxs" /> ignis
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/ilk.svg" alt="" class="avatar-xxs" /> ilk
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/ink.svg" alt="" class="avatar-xxs" /> ink
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/ins.svg" alt="" class="avatar-xxs" /> ins
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/ion.svg" alt="" class="avatar-xxs" /> ion
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/iop.svg" alt="" class="avatar-xxs" /> iop
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/iop.svg" alt="" class="avatar-xxs" /> iop
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/iost.svg" alt="" class="avatar-xxs" /> iost
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/iotx.svg" alt="" class="avatar-xxs" /> iotx
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/iq.svg" alt="" class="avatar-xxs" /> iq
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/itc.svg" alt="" class="avatar-xxs" /> itc
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/jnt.svg" alt="" class="avatar-xxs" /> jnt
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/jpy.svg" alt="" class="avatar-xxs" /> jpy
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/kcs.svg" alt="" class="avatar-xxs" /> kcs
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/kin.svg" alt="" class="avatar-xxs" /> kin
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/klown.svg" alt="" class="avatar-xxs" /> klown
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/kmd.svg" alt="" class="avatar-xxs" /> kmd
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/knc.svg" alt="" class="avatar-xxs" /> knc
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/krb.svg" alt="" class="avatar-xxs" /> krb
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/ksm.svg" alt="" class="avatar-xxs" /> ksm
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/lbc.svg" alt="" class="avatar-xxs" /> lbc
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/lend.svg" alt="" class="avatar-xxs" /> lend
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/leo.svg" alt="" class="avatar-xxs" /> leo
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/link.svg" alt="" class="avatar-xxs" /> link
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/lkk.svg" alt="" class="avatar-xxs" /> lkk
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/loom.svg" alt="" class="avatar-xxs" /> loom
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/lpt.svg" alt="" class="avatar-xxs" /> lpt
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/lrc.svg" alt="" class="avatar-xxs" /> lrc
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/lsk.svg" alt="" class="avatar-xxs" /> lsk
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/ltc.svg" alt="" class="avatar-xxs" /> ltc
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/lun.svg" alt="" class="avatar-xxs" /> lun
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/maid.svg" alt="" class="avatar-xxs" /> maid
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/mana.svg" alt="" class="avatar-xxs" /> mana
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/matic.svg" alt="" class="avatar-xxs" /> matic
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/max.svg" alt="" class="avatar-xxs" /> max
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/mcap.svg" alt="" class="avatar-xxs" /> mcap
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/mco.svg" alt="" class="avatar-xxs" /> mco
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/mda.svg" alt="" class="avatar-xxs" /> mda
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/mds.svg" alt="" class="avatar-xxs" /> mds
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/med.svg" alt="" class="avatar-xxs" /> med
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/meetone.svg" alt="" class="avatar-xxs" /> meetone
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/mft.svg" alt="" class="avatar-xxs" /> mft
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/miota.svg" alt="" class="avatar-xxs" /> miota
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/mith.svg" alt="" class="avatar-xxs" /> mith
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/mkr.svg" alt="" class="avatar-xxs" /> mkr
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/mln.svg" alt="" class="avatar-xxs" /> mln
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/mnx.svg" alt="" class="avatar-xxs" /> mnx
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/mnz.svg" alt="" class="avatar-xxs" /> mnz
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/moac.svg" alt="" class="avatar-xxs" /> moac
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/mod.svg" alt="" class="avatar-xxs" /> mod
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/mona.svg" alt="" class="avatar-xxs" /> mona
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/msr.svg" alt="" class="avatar-xxs" /> msr
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/mth.svg" alt="" class="avatar-xxs" /> mth
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/mtl.svg" alt="" class="avatar-xxs" /> mtl
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/music.svg" alt="" class="avatar-xxs" /> music
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/mzc.svg" alt="" class="avatar-xxs" /> mzc
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/nano.svg" alt="" class="avatar-xxs" /> nano
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/nas.svg" alt="" class="avatar-xxs" /> nas
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/nav.svg" alt="" class="avatar-xxs" /> nav
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/ncash.svg" alt="" class="avatar-xxs" /> ncash
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/ndz.svg" alt="" class="avatar-xxs" /> ndz
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/nebl.svg" alt="" class="avatar-xxs" /> nebl
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/neo.svg" alt="" class="avatar-xxs" /> neo
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/neos.svg" alt="" class="avatar-xxs" /> neos
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/neu.svg" alt="" class="avatar-xxs" /> neu
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/nexo.svg" alt="" class="avatar-xxs" /> nexo
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/ngc.svg" alt="" class="avatar-xxs" /> ngc
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/nio.svg" alt="" class="avatar-xxs" /> nio
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/nkn.svg" alt="" class="avatar-xxs" /> nkn
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/nlc2.svg" alt="" class="avatar-xxs" /> nlc2
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/nlg.svg" alt="" class="avatar-xxs" /> nlg
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/nmc.svg" alt="" class="avatar-xxs" /> nmc
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/nmr.svg" alt="" class="avatar-xxs" /> nmr
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/npxs.svg" alt="" class="avatar-xxs" /> npxs
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/ntbc.svg" alt="" class="avatar-xxs" /> ntbc
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/nuls.svg" alt="" class="avatar-xxs" /> nuls
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/nxs.svg" alt="" class="avatar-xxs" /> nxs
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/nxt.svg" alt="" class="avatar-xxs" /> nxt
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/oax.svg" alt="" class="avatar-xxs" /> oax
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/ok.svg" alt="" class="avatar-xxs" /> ok
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/omg.svg" alt="" class="avatar-xxs" /> omg
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/omni.svg" alt="" class="avatar-xxs" /> omni
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/one.svg" alt="" class="avatar-xxs" /> one
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/ong.svg" alt="" class="avatar-xxs" /> ong
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/ont.svg" alt="" class="avatar-xxs" /> ont
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/oot.svg" alt="" class="avatar-xxs" /> oot
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/ost.svg" alt="" class="avatar-xxs" /> ost
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/ox.svg" alt="" class="avatar-xxs" /> ox
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/oxt.svg" alt="" class="avatar-xxs" /> oxt
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/part.svg" alt="" class="avatar-xxs" /> part
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/pasc.svg" alt="" class="avatar-xxs" /> pasc
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/pasl.svg" alt="" class="avatar-xxs" /> pasl
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/pax.svg" alt="" class="avatar-xxs" /> pax
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/paxg.svg" alt="" class="avatar-xxs" /> paxg
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/pay.svg" alt="" class="avatar-xxs" /> pay
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/payx.svg" alt="" class="avatar-xxs" /> payx
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/pink.svg" alt="" class="avatar-xxs" /> pink
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/pirl.svg" alt="" class="avatar-xxs" /> pirl
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/pivx.svg" alt="" class="avatar-xxs" /> pivx
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/plr.svg" alt="" class="avatar-xxs" /> plr
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/poa.svg" alt="" class="avatar-xxs" /> poa
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/poe.svg" alt="" class="avatar-xxs" /> poe
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/polis.svg" alt="" class="avatar-xxs" /> polis
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/poly.svg" alt="" class="avatar-xxs" /> poly
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/pot.svg" alt="" class="avatar-xxs" /> pot
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/powr.svg" alt="" class="avatar-xxs" /> powr
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/ppc.svg" alt="" class="avatar-xxs" /> ppc
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/ppp.svg" alt="" class="avatar-xxs" /> ppp
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/ppt.svg" alt="" class="avatar-xxs" /> ppt
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/pre.svg" alt="" class="avatar-xxs" /> pre
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/prl.svg" alt="" class="avatar-xxs" /> prl
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/pungo.svg" alt="" class="avatar-xxs" /> pungo
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/pura.svg" alt="" class="avatar-xxs" /> pura
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/qash.svg" alt="" class="avatar-xxs" /> qash
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/qiwi.svg" alt="" class="avatar-xxs" /> qiwi
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/qlc.svg" alt="" class="avatar-xxs" /> qlc
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/qrl.svg" alt="" class="avatar-xxs" /> qrl
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/qsp.svg" alt="" class="avatar-xxs" /> qsp
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/qtum.svg" alt="" class="avatar-xxs" /> qtum
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/r.svg" alt="" class="avatar-xxs" /> r
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/rads.svg" alt="" class="avatar-xxs" /> rads
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/rap.svg" alt="" class="avatar-xxs" /> rap
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/rcn.svg" alt="" class="avatar-xxs" /> rcn
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/rdd.svg" alt="" class="avatar-xxs" /> rdd
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/rdn.svg" alt="" class="avatar-xxs" /> rdn
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/ren.svg" alt="" class="avatar-xxs" /> ren
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/rep.svg" alt="" class="avatar-xxs" /> rep
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/repv2.svg" alt="" class="avatar-xxs" /> repv2
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/req.svg" alt="" class="avatar-xxs" /> req
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/rhoc.svg" alt="" class="avatar-xxs" /> rhoc
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/ric.svg" alt="" class="avatar-xxs" /> ric
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/rise.svg" alt="" class="avatar-xxs" /> rise
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/rlc.svg" alt="" class="avatar-xxs" /> rlc
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/rpx.svg" alt="" class="avatar-xxs" /> rpx
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/rub.svg" alt="" class="avatar-xxs" /> rub
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/rvn.svg" alt="" class="avatar-xxs" /> rvn
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/ryo.svg" alt="" class="avatar-xxs" /> ryo
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/safe.svg" alt="" class="avatar-xxs" /> safe
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/safemoon.svg" alt="" class="avatar-xxs" /> safemoon
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/sai.svg" alt="" class="avatar-xxs" /> sai
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/salt.svg" alt="" class="avatar-xxs" /> salt
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/san.svg" alt="" class="avatar-xxs" /> san
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/sand.svg" alt="" class="avatar-xxs" /> sand
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/sbd.svg" alt="" class="avatar-xxs" /> sbd
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/sberbank.svg" alt="" class="avatar-xxs" /> sberbank
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/sc.svg" alt="" class="avatar-xxs" /> sc
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/shift.svg" alt="" class="avatar-xxs" /> shift
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/sib.svg" alt="" class="avatar-xxs" /> sib
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/sin.svg" alt="" class="avatar-xxs" /> sin
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/skl.svg" alt="" class="avatar-xxs" /> skl
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/sky.svg" alt="" class="avatar-xxs" /> sky
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/slr.svg" alt="" class="avatar-xxs" /> slr
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/sls.svg" alt="" class="avatar-xxs" /> sls
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/smart.svg" alt="" class="avatar-xxs" /> smart
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/sngls.svg" alt="" class="avatar-xxs" /> sngls
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/snm.svg" alt="" class="avatar-xxs" /> snm
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/snt.svg" alt="" class="avatar-xxs" /> snt
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/snx.svg" alt="" class="avatar-xxs" /> snx
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/soc.svg" alt="" class="avatar-xxs" /> soc
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/sol.svg" alt="" class="avatar-xxs" /> sol
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/spacehbit.svg" alt="" class="avatar-xxs" /> spacehbit
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/spank.svg" alt="" class="avatar-xxs" /> spank
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/sphtx.svg" alt="" class="avatar-xxs" /> sphtx
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/srn.svg" alt="" class="avatar-xxs" /> srn
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/stak.svg" alt="" class="avatar-xxs" /> stak
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/start.svg" alt="" class="avatar-xxs" /> start
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/steem.svg" alt="" class="avatar-xxs" /> steem
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/storj.svg" alt="" class="avatar-xxs" /> storj
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/storm.svg" alt="" class="avatar-xxs" /> storm
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/stox.svg" alt="" class="avatar-xxs" /> stox
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/stq.svg" alt="" class="avatar-xxs" /> stq
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/strat.svg" alt="" class="avatar-xxs" /> strat
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/stx.svg" alt="" class="avatar-xxs" /> stx
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/sub.svg" alt="" class="avatar-xxs" /> sub
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/sumo.svg" alt="" class="avatar-xxs" /> sumo
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/sushi.svg" alt="" class="avatar-xxs" /> sushi
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/sys.svg" alt="" class="avatar-xxs" /> sys
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/taas.svg" alt="" class="avatar-xxs" /> taas
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/tau.svg" alt="" class="avatar-xxs" /> tau
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/tbx.svg" alt="" class="avatar-xxs" /> tbx
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/tel.svg" alt="" class="avatar-xxs" /> tel
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/ten.svg" alt="" class="avatar-xxs" /> ten
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/tern.svg" alt="" class="avatar-xxs" /> tern
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/tgch.svg" alt="" class="avatar-xxs" /> tgch
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/theta.svg" alt="" class="avatar-xxs" /> theta
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/tix.svg" alt="" class="avatar-xxs" /> tix
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/tkn.svg" alt="" class="avatar-xxs" /> tkn
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/tks.svg" alt="" class="avatar-xxs" /> tks
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/tnb.svg" alt="" class="avatar-xxs" /> tnb
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/tnc.svg" alt="" class="avatar-xxs" /> tnc
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/tnt.svg" alt="" class="avatar-xxs" /> tnt
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/tomo.svg" alt="" class="avatar-xxs" /> tomo
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/tpay.svg" alt="" class="avatar-xxs" /> tpay
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/trig.svg" alt="" class="avatar-xxs" /> trig
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/trtl.svg" alt="" class="avatar-xxs" /> trtl
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/trx.svg" alt="" class="avatar-xxs" /> trx
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/tusd.svg" alt="" class="avatar-xxs" /> tusd
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/tzc.svg" alt="" class="avatar-xxs" /> tzc
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/ubq.svg" alt="" class="avatar-xxs" /> ubq
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/uma.svg" alt="" class="avatar-xxs" /> uma
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/uni.svg" alt="" class="avatar-xxs" /> uni
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/unity.svg" alt="" class="avatar-xxs" /> unity
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/usd.svg" alt="" class="avatar-xxs" /> usd
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/usdc.svg" alt="" class="avatar-xxs" /> usdc
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/usdt.svg" alt="" class="avatar-xxs" /> usdt
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/utk.svg" alt="" class="avatar-xxs" /> utk
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/veri.svg" alt="" class="avatar-xxs" /> veri
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/vet.svg" alt="" class="avatar-xxs" /> vet
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/via.svg" alt="" class="avatar-xxs" /> via
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/vib.svg" alt="" class="avatar-xxs" /> vib
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/vibe.svg" alt="" class="avatar-xxs" /> vibe
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/vivo.svg" alt="" class="avatar-xxs" /> vivo
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/vrc.svg" alt="" class="avatar-xxs" /> vrc
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/vrsc.svg" alt="" class="avatar-xxs" /> vrsc
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/vtc.svg" alt="" class="avatar-xxs" /> vtc
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/vtho.svg" alt="" class="avatar-xxs" /> vtho
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/wabi.svg" alt="" class="avatar-xxs" /> wabi
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/wan.svg" alt="" class="avatar-xxs" /> wan
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/waves.svg" alt="" class="avatar-xxs" /> waves
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/wax.svg" alt="" class="avatar-xxs" /> wax
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/wbtc.svg" alt="" class="avatar-xxs" /> wbtc
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/wgr.svg" alt="" class="avatar-xxs" /> wgr
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/wicc.svg" alt="" class="avatar-xxs" /> wicc
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/wings.svg" alt="" class="avatar-xxs" /> wings
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/wpr.svg" alt="" class="avatar-xxs" /> wpr
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/wtc.svg" alt="" class="avatar-xxs" /> wtc
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/x.svg" alt="" class="avatar-xxs" /> x
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/xas.svg" alt="" class="avatar-xxs" /> xas
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/xbc.svg" alt="" class="avatar-xxs" /> xbc
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/xbp.svg" alt="" class="avatar-xxs" /> xbp
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/xby.svg" alt="" class="avatar-xxs" /> xby
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/xcp.svg" alt="" class="avatar-xxs" /> xcp
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/xdn.svg" alt="" class="avatar-xxs" /> xdn
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/xem.svg" alt="" class="avatar-xxs" /> xem
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/xin.svg" alt="" class="avatar-xxs" /> xin
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/xlm.svg" alt="" class="avatar-xxs" /> xlm
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/xmcc.svg" alt="" class="avatar-xxs" /> xmcc
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/xmg.svg" alt="" class="avatar-xxs" /> xmg
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/xmo.svg" alt="" class="avatar-xxs" /> xmo
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/xmr.svg" alt="" class="avatar-xxs" /> xmr
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/xmy.svg" alt="" class="avatar-xxs" /> xmy
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/xp.svg" alt="" class="avatar-xxs" /> xp
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/xpa.svg" alt="" class="avatar-xxs" /> xpa
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/xpm.svg" alt="" class="avatar-xxs" /> xpm
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/xpr.svg" alt="" class="avatar-xxs" /> xpr
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/xrp.svg" alt="" class="avatar-xxs" /> xrp
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/xsg.svg" alt="" class="avatar-xxs" /> xsg
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/xtz.svg" alt="" class="avatar-xxs" /> xtz
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/xuc.svg" alt="" class="avatar-xxs" /> xuc
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/xvc.svg" alt="" class="avatar-xxs" /> xvc
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/xvg.svg" alt="" class="avatar-xxs" /> xvg
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/xzc.svg" alt="" class="avatar-xxs" /> xzc
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/yfi.svg" alt="" class="avatar-xxs" /> yfi
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/yoyow.svg" alt="" class="avatar-xxs" /> yoyow
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/zcl.svg" alt="" class="avatar-xxs" /> zcl
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/zec.svg" alt="" class="avatar-xxs" /> zec
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/zel.svg" alt="" class="avatar-xxs" /> zel
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/zen.svg" alt="" class="avatar-xxs" /> zen
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/zest.svg" alt="" class="avatar-xxs" /> zest
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/zil.svg" alt="" class="avatar-xxs" /> zil
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/zilla.svg" alt="" class="avatar-xxs" /> zilla
                                            </div>
                                        </div>
                                        <div class="col-xl-3 col-lg-4 col-sm-6">
                                            <div class="text-muted hstack gap-2">
                                                <img src="assets/images/svg/crypto-icons/zrx.svg" alt="" class="avatar-xxs" /> zrx
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div><!--end col-->
                    </div><!--end row-->

                </div>
                <!-- container-fluid -->
            </div>
            <!-- End Page-content -->

            <footer class="footer">
                <div class="container-fluid">
                    <div class="row">
                        <div class="col-sm-6">
                            <script>document.write(new Date().getFullYear())</script> © Velzon.
                        </div>
                        <div class="col-sm-6">
                            <div class="text-sm-end d-none d-sm-block">
                                Design & Develop by Themesbrand
                            </div>
                        </div>
                    </div>
                </div>
            </footer>
        </div>
        <!-- end main content-->

    </div>
    <!-- END layout-wrapper -->



    <!--start back-to-top-->
    <button onclick="topFunction()" class="btn btn-danger btn-icon" id="back-to-top">
        <i class="ri-arrow-up-line"></i>
    </button>
    <!--end back-to-top-->

    <!--preloader-->
    <div id="preloader">
        <div id="status">
            <div class="spinner-border text-primary avatar-sm" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
        </div>
    </div>

    <div class="customizer-setting d-none d-md-block">
        <div class="btn-info rounded-pill shadow-lg btn btn-icon btn-lg p-2" data-bs-toggle="offcanvas" data-bs-target="#theme-settings-offcanvas" aria-controls="theme-settings-offcanvas">
            <i class='mdi mdi-spin mdi-cog-outline fs-22'></i>
        </div>
    </div>

    <!-- Theme Settings -->
    <div class="offcanvas offcanvas-end border-0" tabindex="-1" id="theme-settings-offcanvas">
        <div class="d-flex align-items-center bg-primary bg-gradient p-3 offcanvas-header">
            <h5 class="m-0 me-2 text-white">Theme Customizer</h5>

            <button type="button" class="btn-close btn-close-white ms-auto" id="customizerclose-btn" data-bs-dismiss="offcanvas" aria-label="Close"></button>
        </div>
        <div class="offcanvas-body p-0">
            <div data-simplebar class="h-100">
                <div class="p-4">
                    <h6 class="mb-0 fw-semibold text-uppercase">Layout</h6>
                    <p class="text-muted">Choose your layout</p>

                    <div class="row gy-3">
                        <div class="col-4">
                            <div class="form-check card-radio">
                                <input id="customizer-layout01" name="data-layout" type="radio" value="vertical" class="form-check-input">
                                <label class="form-check-label p-0 avatar-md w-100 material-shadow" for="customizer-layout01">
                                    <span class="d-flex gap-1 h-100">
                                        <span class="flex-shrink-0">
                                            <span class="bg-light d-flex h-100 flex-column gap-1 p-1">
                                                <span class="d-block p-1 px-2 bg-primary-subtle rounded mb-2"></span>
                                                <span class="d-block p-1 px-2 pb-0 bg-primary-subtle"></span>
                                                <span class="d-block p-1 px-2 pb-0 bg-primary-subtle"></span>
                                                <span class="d-block p-1 px-2 pb-0 bg-primary-subtle"></span>
                                            </span>
                                        </span>
                                        <span class="flex-grow-1">
                                            <span class="d-flex h-100 flex-column">
                                                <span class="bg-light d-block p-1"></span>
                                                <span class="bg-light d-block p-1 mt-auto"></span>
                                            </span>
                                        </span>
                                    </span>
                                </label>
                            </div>
                            <h5 class="fs-13 text-center mt-2">Vertical</h5>
                        </div>
                        <div class="col-4">
                            <div class="form-check card-radio">
                                <input id="customizer-layout02" name="data-layout" type="radio" value="horizontal" class="form-check-input">
                                <label class="form-check-label p-0 avatar-md w-100 material-shadow" for="customizer-layout02">
                                    <span class="d-flex h-100 flex-column gap-1">
                                        <span class="bg-light d-flex p-1 gap-1 align-items-center">
                                            <span class="d-block p-1 bg-primary-subtle rounded me-1"></span>
                                            <span class="d-block p-1 pb-0 px-2 bg-primary-subtle ms-auto"></span>
                                            <span class="d-block p-1 pb-0 px-2 bg-primary-subtle"></span>
                                        </span>
                                        <span class="bg-light d-block p-1"></span>
                                        <span class="bg-light d-block p-1 mt-auto"></span>
                                    </span>
                                </label>
                            </div>
                            <h5 class="fs-13 text-center mt-2">Horizontal</h5>
                        </div>
                        <div class="col-4">
                            <div class="form-check card-radio">
                                <input id="customizer-layout03" name="data-layout" type="radio" value="twocolumn" class="form-check-input">
                                <label class="form-check-label p-0 avatar-md w-100 material-shadow" for="customizer-layout03">
                                    <span class="d-flex gap-1 h-100">
                                        <span class="flex-shrink-0">
                                            <span class="bg-light d-flex h-100 flex-column gap-1">
                                                <span class="d-block p-1 bg-primary-subtle mb-2"></span>
                                                <span class="d-block p-1 pb-0 bg-primary-subtle"></span>
                                                <span class="d-block p-1 pb-0 bg-primary-subtle"></span>
                                                <span class="d-block p-1 pb-0 bg-primary-subtle"></span>
                                            </span>
                                        </span>
                                        <span class="flex-shrink-0">
                                            <span class="bg-light d-flex h-100 flex-column gap-1 p-1">
                                                <span class="d-block p-1 px-2 pb-0 bg-primary-subtle"></span>
                                                <span class="d-block p-1 px-2 pb-0 bg-primary-subtle"></span>
                                                <span class="d-block p-1 px-2 pb-0 bg-primary-subtle"></span>
                                                <span class="d-block p-1 px-2 pb-0 bg-primary-subtle"></span>
                                            </span>
                                        </span>
                                        <span class="flex-grow-1">
                                            <span class="d-flex h-100 flex-column">
                                                <span class="bg-light d-block p-1"></span>
                                                <span class="bg-light d-block p-1 mt-auto"></span>
                                            </span>
                                        </span>
                                    </span>
                                </label>
                            </div>
                            <h5 class="fs-13 text-center mt-2">Two Column</h5>
                        </div>
                        <!-- end col -->

                        <div class="col-4">
                            <div class="form-check card-radio">
                                <input id="customizer-layout04" name="data-layout" type="radio" value="semibox" class="form-check-input">
                                <label class="form-check-label p-0 avatar-md w-100 material-shadow" for="customizer-layout04">
                                    <span class="d-flex gap-1 h-100">
                                        <span class="flex-shrink-0 p-1">
                                            <span class="bg-light d-flex h-100 flex-column gap-1 p-1">
                                                <span class="d-block p-1 px-2 bg-primary-subtle rounded mb-2"></span>
                                                <span class="d-block p-1 px-2 pb-0 bg-primary-subtle"></span>
                                                <span class="d-block p-1 px-2 pb-0 bg-primary-subtle"></span>
                                                <span class="d-block p-1 px-2 pb-0 bg-primary-subtle"></span>
                                            </span>
                                        </span>
                                        <span class="flex-grow-1">
                                            <span class="d-flex h-100 flex-column pt-1 pe-2">
                                                <span class="bg-light d-block p-1"></span>
                                                <span class="bg-light d-block p-1 mt-auto"></span>
                                            </span>
                                        </span>
                                    </span>
                                </label>
                            </div>
                            <h5 class="fs-13 text-center mt-2">Semi Box</h5>
                        </div>
                        <!-- end col -->
                    </div>

                    <div class="form-check form-switch form-switch-md mb-3 mt-4">
                        <input type="checkbox" class="form-check-input" id="sidebarUserProfile">
                        <label class="form-check-label" for="sidebarUserProfile">Sidebar User Profile Avatar</label>
                    </div>

                    <h6 class="mt-4 mb-0 fw-semibold text-uppercase">Theme</h6>
                    <p class="text-muted">Choose your suitable Theme.</p>

                    <div class="row">
                        <div class="col-6">
                            <div class="form-check card-radio">
                                <input id="customizer-theme01" name="data-theme" type="radio" value="default" class="form-check-input">
                                <label class="form-check-label p-0" for="customizer-theme01">
                                    <img src="../../assets/images/demo/default.png" alt="" class="img-fluid">
                                </label>
                            </div>
                            <h5 class="fs-13 text-center fw-medium mt-2">Default</h5>
                        </div>
                        <div class="col-6">
                            <div class="form-check card-radio">
                                <input id="customizer-theme02" name="data-theme" type="radio" value="saas" class="form-check-input">
                                <label class="form-check-label p-0" for="customizer-theme02">
                                    <img src="../../assets/images/demo/saas.png" alt="" class="img-fluid">
                                </label>
                            </div>
                            <h5 class="fs-13 text-center fw-medium mt-2">Sass</h5>
                        </div>
                        <div class="col-6">
                            <div class="form-check card-radio">
                                <input id="customizer-theme03" name="data-theme" type="radio" value="corporate" class="form-check-input">
                                <label class="form-check-label p-0" for="customizer-theme03">
                                    <img src="../../assets/images/demo/corporate.png" alt="" class="img-fluid">
                                </label>
                            </div>
                            <h5 class="fs-13 text-center fw-medium mt-2">Corporate</h5>
                        </div>
                        <div class="col-6">
                            <div class="form-check card-radio">
                                <input id="customizer-theme04" name="data-theme" type="radio" value="galaxy" class="form-check-input">
                                <label class="form-check-label p-0" for="customizer-theme04">
                                    <img src="../../assets/images/demo/galaxy.png" alt="" class="img-fluid">
                                </label>
                            </div>
                            <h5 class="fs-13 text-center fw-medium mt-2">Galaxy</h5>
                        </div>
                        <div class="col-6">
                            <div class="form-check card-radio">
                                <input id="customizer-theme05" name="data-theme" type="radio" value="material" class="form-check-input">
                                <label class="form-check-label p-0" for="customizer-theme05">
                                    <img src="../../assets/images/demo/material.png" alt="" class="img-fluid">
                                </label>
                            </div>
                            <h5 class="fs-13 text-center fw-medium mt-2">Material</h5>
                        </div>
                        <div class="col-6">
                            <div class="form-check card-radio">
                                <input id="customizer-theme06" name="data-theme" type="radio" value="creative" class="form-check-input">
                                <label class="form-check-label p-0" for="customizer-theme06">
                                    <img src="../../assets/images/demo/creative.png" alt="" class="img-fluid">
                                </label>
                            </div>
                            <h5 class="fs-13 text-center fw-medium mt-2">Creative</h5>
                        </div>
                        <div class="col-6">
                            <div class="form-check card-radio">
                                <input id="customizer-theme07" name="data-theme" type="radio" value="minimal" class="form-check-input">
                                <label class="form-check-label p-0" for="customizer-theme07">
                                    <img src="../../assets/images/demo/minimal.png" alt="" class="img-fluid">
                                </label>
                            </div>
                            <h5 class="fs-13 text-center fw-medium mt-2">Minimal</h5>
                        </div>
                        <div class="col-6">
                            <div class="form-check card-radio">
                                <input id="customizer-theme08" name="data-theme" type="radio" value="modern" class="form-check-input">
                                <label class="form-check-label p-0" for="customizer-theme08">
                                    <img src="../../assets/images/demo/modern.png" alt="" class="img-fluid">
                                </label>
                            </div>
                            <h5 class="fs-13 text-center fw-medium mt-2">Modern</h5>
                        </div>
                        <!-- end col -->
                        <div class="col-6">
                            <div class="form-check card-radio">
                                <input id="customizer-theme09" name="data-theme" type="radio" value="interactive" class="form-check-input">
                                <label class="form-check-label p-0" for="customizer-theme09">
                                    <img src="../../assets/images/demo/interactive.png" alt="" class="img-fluid">
                                </label>
                            </div>
                            <h5 class="fs-13 text-center fw-medium mt-2">Interactive</h5>
                        </div><!-- end col -->

                        <div class="col-6">
                            <div class="form-check card-radio">
                                <input id="customizer-theme10" name="data-theme" type="radio" value="classic" class="form-check-input">
                                <label class="form-check-label p-0" for="customizer-theme10">
                                    <img src="../../assets/images/demo/classic.png" alt="" class="img-fluid">
                                </label>
                            </div>
                            <h5 class="fs-13 text-center fw-medium mt-2">Classic</h5>
                        </div><!-- end col -->

                        <div class="col-6">
                            <div class="form-check card-radio">
                                <input id="customizer-theme11" name="data-theme" type="radio" value="vintage" class="form-check-input">
                                <label class="form-check-label p-0" for="customizer-theme11">
                                    <img src="../../assets/images/demo/vintage.png" alt="" class="img-fluid">
                                </label>
                            </div>
                            <h5 class="fs-13 text-center fw-medium mt-2">Vintage</h5>
                        </div><!-- end col -->
                    </div>

                    <h6 class="mt-4 mb-0 fw-semibold text-uppercase">Color Scheme</h6>
                    <p class="text-muted">Choose Light or Dark Scheme.</p>

                    <div class="colorscheme-cardradio">
                        <div class="row">
                            <div class="col-4">
                                <div class="form-check card-radio">
                                    <input class="form-check-input" type="radio" name="data-bs-theme" id="layout-mode-light" value="light">
                                    <label class="form-check-label p-0 avatar-md w-100 material-shadow" for="layout-mode-light">
                                        <span class="d-flex gap-1 h-100">
                                            <span class="flex-shrink-0">
                                                <span class="bg-light d-flex h-100 flex-column gap-1 p-1">
                                                    <span class="d-block p-1 px-2 bg-primary-subtle rounded mb-2"></span>
                                                    <span class="d-block p-1 px-2 pb-0 bg-primary-subtle"></span>
                                                    <span class="d-block p-1 px-2 pb-0 bg-primary-subtle"></span>
                                                    <span class="d-block p-1 px-2 pb-0 bg-primary-subtle"></span>
                                                </span>
                                            </span>
                                            <span class="flex-grow-1">
                                                <span class="d-flex h-100 flex-column">
                                                    <span class="bg-light d-block p-1"></span>
                                                    <span class="bg-light d-block p-1 mt-auto"></span>
                                                </span>
                                            </span>
                                        </span>
                                    </label>
                                </div>
                                <h5 class="fs-13 text-center mt-2">Light</h5>
                            </div>

                            <div class="col-4">
                                <div class="form-check card-radio dark">
                                    <input class="form-check-input" type="radio" name="data-bs-theme" id="layout-mode-dark" value="dark">
                                    <label class="form-check-label p-0 avatar-md w-100 bg-dark material-shadow" for="layout-mode-dark">
                                        <span class="d-flex gap-1 h-100">
                                            <span class="flex-shrink-0">
                                                <span class="bg-white bg-opacity-10 d-flex h-100 flex-column gap-1 p-1">
                                                    <span class="d-block p-1 px-2 bg-white bg-opacity-10 rounded mb-2"></span>
                                                    <span class="d-block p-1 px-2 pb-0 bg-white bg-opacity-10"></span>
                                                    <span class="d-block p-1 px-2 pb-0 bg-white bg-opacity-10"></span>
                                                    <span class="d-block p-1 px-2 pb-0 bg-white bg-opacity-10"></span>
                                                </span>
                                            </span>
                                            <span class="flex-grow-1">
                                                <span class="d-flex h-100 flex-column">
                                                    <span class="bg-white bg-opacity-10 d-block p-1"></span>
                                                    <span class="bg-white bg-opacity-10 d-block p-1 mt-auto"></span>
                                                </span>
                                            </span>
                                        </span>
                                    </label>
                                </div>
                                <h5 class="fs-13 text-center mt-2">Dark</h5>
                            </div>
                        </div>
                    </div>

                    <div id="sidebar-visibility">
                        <h6 class="mt-4 mb-0 fw-semibold text-uppercase">Sidebar Visibility</h6>
                        <p class="text-muted">Choose show or Hidden sidebar.</p>
                
                        <div class="row">
                            <div class="col-4">
                                <div class="form-check card-radio">
                                    <input class="form-check-input" type="radio" name="data-sidebar-visibility" id="sidebar-visibility-show" value="show">
                                    <label class="form-check-label p-0 avatar-md w-100 material-shadow" for="sidebar-visibility-show">
                                        <span class="d-flex gap-1 h-100">
                                            <span class="flex-shrink-0 p-1">
                                                <span class="bg-light d-flex h-100 flex-column gap-1 p-1">
                                                    <span class="d-block p-1 px-2 bg-primary-subtle rounded mb-2"></span>
                                                    <span class="d-block p-1 px-2 pb-0 bg-primary-subtle"></span>
                                                    <span class="d-block p-1 px-2 pb-0 bg-primary-subtle"></span>
                                                    <span class="d-block p-1 px-2 pb-0 bg-primary-subtle"></span>
                                                </span>
                                            </span>
                                            <span class="flex-grow-1">
                                                <span class="d-flex h-100 flex-column pt-1 pe-2">
                                                    <span class="bg-light d-block p-1"></span>
                                                    <span class="bg-light d-block p-1 mt-auto"></span>
                                                </span>
                                            </span>
                                        </span>
                                    </label>
                                </div>
                                <h5 class="fs-13 text-center mt-2">Show</h5>
                            </div>
                            <div class="col-4">
                                <div class="form-check card-radio">
                                    <input class="form-check-input" type="radio" name="data-sidebar-visibility" id="sidebar-visibility-hidden" value="hidden">
                                    <label class="form-check-label p-0 avatar-md w-100 px-2 material-shadow" for="sidebar-visibility-hidden">
                                        <span class="d-flex gap-1 h-100">
                                            <span class="flex-grow-1">
                                                <span class="d-flex h-100 flex-column pt-1 px-2">
                                                    <span class="bg-light d-block p-1"></span>
                                                    <span class="bg-light d-block p-1 mt-auto"></span>
                                                </span>
                                            </span>
                                        </span>
                                    </label>
                                </div>
                                <h5 class="fs-13 text-center mt-2">Hidden</h5>
                            </div>
                        </div>
                    </div>

                    <div id="layout-width">
                        <h6 class="mt-4 mb-0 fw-semibold text-uppercase">Layout Width</h6>
                        <p class="text-muted">Choose Fluid or Boxed layout.</p>

                        <div class="row">
                            <div class="col-4">
                                <div class="form-check card-radio">
                                    <input class="form-check-input" type="radio" name="data-layout-width" id="layout-width-fluid" value="fluid">
                                    <label class="form-check-label p-0 avatar-md w-100 material-shadow" for="layout-width-fluid">
                                        <span class="d-flex gap-1 h-100">
                                            <span class="flex-shrink-0">
                                                <span class="bg-light d-flex h-100 flex-column gap-1 p-1">
                                                    <span class="d-block p-1 px-2 bg-primary-subtle rounded mb-2"></span>
                                                    <span class="d-block p-1 px-2 pb-0 bg-primary-subtle"></span>
                                                    <span class="d-block p-1 px-2 pb-0 bg-primary-subtle"></span>
                                                    <span class="d-block p-1 px-2 pb-0 bg-primary-subtle"></span>
                                                </span>
                                            </span>
                                            <span class="flex-grow-1">
                                                <span class="d-flex h-100 flex-column">
                                                    <span class="bg-light d-block p-1"></span>
                                                    <span class="bg-light d-block p-1 mt-auto"></span>
                                                </span>
                                            </span>
                                        </span>
                                    </label>
                                </div>
                                <h5 class="fs-13 text-center mt-2">Fluid</h5>
                            </div>
                            <div class="col-4">
                                <div class="form-check card-radio">
                                    <input class="form-check-input" type="radio" name="data-layout-width" id="layout-width-boxed" value="boxed">
                                    <label class="form-check-label p-0 avatar-md w-100 px-2 material-shadow" for="layout-width-boxed">
                                        <span class="d-flex gap-1 h-100 border-start border-end">
                                            <span class="flex-shrink-0">
                                                <span class="bg-light d-flex h-100 flex-column gap-1 p-1">
                                                    <span class="d-block p-1 px-2 bg-primary-subtle rounded mb-2"></span>
                                                    <span class="d-block p-1 px-2 pb-0 bg-primary-subtle"></span>
                                                    <span class="d-block p-1 px-2 pb-0 bg-primary-subtle"></span>
                                                    <span class="d-block p-1 px-2 pb-0 bg-primary-subtle"></span>
                                                </span>
                                            </span>
                                            <span class="flex-grow-1">
                                                <span class="d-flex h-100 flex-column">
                                                    <span class="bg-light d-block p-1"></span>
                                                    <span class="bg-light d-block p-1 mt-auto"></span>
                                                </span>
                                            </span>
                                        </span>
                                    </label>
                                </div>
                                <h5 class="fs-13 text-center mt-2">Boxed</h5>
                            </div>
                        </div>
                    </div>

                    <div id="layout-position">
                        <h6 class="mt-4 mb-0 fw-semibold text-uppercase">Layout Position</h6>
                        <p class="text-muted">Choose Fixed or Scrollable Layout Position.</p>

                        <div class="btn-group radio" role="group">
                            <input type="radio" class="btn-check" name="data-layout-position" id="layout-position-fixed" value="fixed">
                            <label class="btn btn-light w-sm" for="layout-position-fixed">Fixed</label>

                            <input type="radio" class="btn-check" name="data-layout-position" id="layout-position-scrollable" value="scrollable">
                            <label class="btn btn-light w-sm ms-0" for="layout-position-scrollable">Scrollable</label>
                        </div>
                    </div>
                    <h6 class="mt-4 mb-0 fw-semibold text-uppercase">Topbar Color</h6>
                    <p class="text-muted">Choose Light or Dark Topbar Color.</p>

                    <div class="row">
                        <div class="col-4">
                            <div class="form-check card-radio">
                                <input class="form-check-input" type="radio" name="data-topbar" id="topbar-color-light" value="light">
                                <label class="form-check-label p-0 avatar-md w-100 material-shadow" for="topbar-color-light">
                                    <span class="d-flex gap-1 h-100">
                                        <span class="flex-shrink-0">
                                            <span class="bg-light d-flex h-100 flex-column gap-1 p-1">
                                                <span class="d-block p-1 px-2 bg-primary-subtle rounded mb-2"></span>
                                                <span class="d-block p-1 px-2 pb-0 bg-primary-subtle"></span>
                                                <span class="d-block p-1 px-2 pb-0 bg-primary-subtle"></span>
                                                <span class="d-block p-1 px-2 pb-0 bg-primary-subtle"></span>
                                            </span>
                                        </span>
                                        <span class="flex-grow-1">
                                            <span class="d-flex h-100 flex-column">
                                                <span class="bg-light d-block p-1"></span>
                                                <span class="bg-light d-block p-1 mt-auto"></span>
                                            </span>
                                        </span>
                                    </span>
                                </label>
                            </div>
                            <h5 class="fs-13 text-center mt-2">Light</h5>
                        </div>
                        <div class="col-4">
                            <div class="form-check card-radio">
                                <input class="form-check-input" type="radio" name="data-topbar" id="topbar-color-dark" value="dark">
                                <label class="form-check-label p-0 avatar-md w-100 material-shadow" for="topbar-color-dark">
                                    <span class="d-flex gap-1 h-100">
                                        <span class="flex-shrink-0">
                                            <span class="bg-light d-flex h-100 flex-column gap-1 p-1">
                                                <span class="d-block p-1 px-2 bg-primary-subtle rounded mb-2"></span>
                                                <span class="d-block p-1 px-2 pb-0 bg-primary-subtle"></span>
                                                <span class="d-block p-1 px-2 pb-0 bg-primary-subtle"></span>
                                                <span class="d-block p-1 px-2 pb-0 bg-primary-subtle"></span>
                                            </span>
                                        </span>
                                        <span class="flex-grow-1">
                                            <span class="d-flex h-100 flex-column">
                                                <span class="bg-primary d-block p-1"></span>
                                                <span class="bg-light d-block p-1 mt-auto"></span>
                                            </span>
                                        </span>
                                    </span>
                                </label>
                            </div>
                            <h5 class="fs-13 text-center mt-2">Dark</h5>
                        </div>
                    </div>

                    <div id="sidebar-size">
                        <h6 class="mt-4 mb-0 fw-semibold text-uppercase">Sidebar Size</h6>
                        <p class="text-muted">Choose a size of Sidebar.</p>

                        <div class="row">
                            <div class="col-4">
                                <div class="form-check sidebar-setting card-radio">
                                    <input class="form-check-input" type="radio" name="data-sidebar-size" id="sidebar-size-default" value="lg">
                                    <label class="form-check-label p-0 avatar-md w-100 material-shadow" for="sidebar-size-default">
                                        <span class="d-flex gap-1 h-100">
                                            <span class="flex-shrink-0">
                                                <span class="bg-light d-flex h-100 flex-column gap-1 p-1">
                                                    <span class="d-block p-1 px-2 bg-primary-subtle rounded mb-2"></span>
                                                    <span class="d-block p-1 px-2 pb-0 bg-primary-subtle"></span>
                                                    <span class="d-block p-1 px-2 pb-0 bg-primary-subtle"></span>
                                                    <span class="d-block p-1 px-2 pb-0 bg-primary-subtle"></span>
                                                </span>
                                            </span>
                                            <span class="flex-grow-1">
                                                <span class="d-flex h-100 flex-column">
                                                    <span class="bg-light d-block p-1"></span>
                                                    <span class="bg-light d-block p-1 mt-auto"></span>
                                                </span>
                                            </span>
                                        </span>
                                    </label>
                                </div>
                                <h5 class="fs-13 text-center mt-2">Default</h5>
                            </div>

                            <div class="col-4">
                                <div class="form-check sidebar-setting card-radio">
                                    <input class="form-check-input" type="radio" name="data-sidebar-size" id="sidebar-size-compact" value="md">
                                    <label class="form-check-label p-0 avatar-md w-100 material-shadow" for="sidebar-size-compact">
                                        <span class="d-flex gap-1 h-100">
                                            <span class="flex-shrink-0">
                                                <span class="bg-light d-flex h-100 flex-column gap-1 p-1">
                                                    <span class="d-block p-1 bg-primary-subtle rounded mb-2"></span>
                                                    <span class="d-block p-1 pb-0 bg-primary-subtle"></span>
                                                    <span class="d-block p-1 pb-0 bg-primary-subtle"></span>
                                                    <span class="d-block p-1 pb-0 bg-primary-subtle"></span>
                                                </span>
                                            </span>
                                            <span class="flex-grow-1">
                                                <span class="d-flex h-100 flex-column">
                                                    <span class="bg-light d-block p-1"></span>
                                                    <span class="bg-light d-block p-1 mt-auto"></span>
                                                </span>
                                            </span>
                                        </span>
                                    </label>
                                </div>
                                <h5 class="fs-13 text-center mt-2">Compact</h5>
                            </div>

                            <div class="col-4">
                                <div class="form-check sidebar-setting card-radio">
                                    <input class="form-check-input" type="radio" name="data-sidebar-size" id="sidebar-size-small" value="sm">
                                    <label class="form-check-label p-0 avatar-md w-100 material-shadow" for="sidebar-size-small">
                                        <span class="d-flex gap-1 h-100">
                                            <span class="flex-shrink-0">
                                                <span class="bg-light d-flex h-100 flex-column gap-1">
                                                    <span class="d-block p-1 bg-primary-subtle mb-2"></span>
                                                    <span class="d-block p-1 pb-0 bg-primary-subtle"></span>
                                                    <span class="d-block p-1 pb-0 bg-primary-subtle"></span>
                                                    <span class="d-block p-1 pb-0 bg-primary-subtle"></span>
                                                </span>
                                            </span>
                                            <span class="flex-grow-1">
                                                <span class="d-flex h-100 flex-column">
                                                    <span class="bg-light d-block p-1"></span>
                                                    <span class="bg-light d-block p-1 mt-auto"></span>
                                                </span>
                                            </span>
                                        </span>
                                    </label>
                                </div>
                                <h5 class="fs-13 text-center mt-2">Small (Icon View)</h5>
                            </div>

                            <div class="col-4">
                                <div class="form-check sidebar-setting card-radio">
                                    <input class="form-check-input" type="radio" name="data-sidebar-size" id="sidebar-size-small-hover" value="sm-hover">
                                    <label class="form-check-label p-0 avatar-md w-100 material-shadow" for="sidebar-size-small-hover">
                                        <span class="d-flex gap-1 h-100">
                                            <span class="flex-shrink-0">
                                                <span class="bg-light d-flex h-100 flex-column gap-1">
                                                    <span class="d-block p-1 bg-primary-subtle mb-2"></span>
                                                    <span class="d-block p-1 pb-0 bg-primary-subtle"></span>
                                                    <span class="d-block p-1 pb-0 bg-primary-subtle"></span>
                                                    <span class="d-block p-1 pb-0 bg-primary-subtle"></span>
                                                </span>
                                            </span>
                                            <span class="flex-grow-1">
                                                <span class="d-flex h-100 flex-column">
                                                    <span class="bg-light d-block p-1"></span>
                                                    <span class="bg-light d-block p-1 mt-auto"></span>
                                                </span>
                                            </span>
                                        </span>
                                    </label>
                                </div>
                                <h5 class="fs-13 text-center mt-2">Small Hover View</h5>
                            </div>
                        </div>
                    </div>

                    <div id="sidebar-view">
                        <h6 class="mt-4 mb-0 fw-semibold text-uppercase">Sidebar View</h6>
                        <p class="text-muted">Choose Default or Detached Sidebar view.</p>

                        <div class="row">
                            <div class="col-4">
                                <div class="form-check sidebar-setting card-radio">
                                    <input class="form-check-input" type="radio" name="data-layout-style" id="sidebar-view-default" value="default">
                                    <label class="form-check-label p-0 avatar-md w-100 material-shadow" for="sidebar-view-default">
                                        <span class="d-flex gap-1 h-100">
                                            <span class="flex-shrink-0">
                                                <span class="bg-light d-flex h-100 flex-column gap-1 p-1">
                                                    <span class="d-block p-1 px-2 bg-primary-subtle rounded mb-2"></span>
                                                    <span class="d-block p-1 px-2 pb-0 bg-primary-subtle"></span>
                                                    <span class="d-block p-1 px-2 pb-0 bg-primary-subtle"></span>
                                                    <span class="d-block p-1 px-2 pb-0 bg-primary-subtle"></span>
                                                </span>
                                            </span>
                                            <span class="flex-grow-1">
                                                <span class="d-flex h-100 flex-column">
                                                    <span class="bg-light d-block p-1"></span>
                                                    <span class="bg-light d-block p-1 mt-auto"></span>
                                                </span>
                                            </span>
                                        </span>
                                    </label>
                                </div>
                                <h5 class="fs-13 text-center mt-2">Default</h5>
                            </div>
                            <div class="col-4">
                                <div class="form-check sidebar-setting card-radio">
                                    <input class="form-check-input" type="radio" name="data-layout-style" id="sidebar-view-detached" value="detached">
                                    <label class="form-check-label p-0 avatar-md w-100 material-shadow" for="sidebar-view-detached">
                                        <span class="d-flex h-100 flex-column">
                                            <span class="bg-light d-flex p-1 gap-1 align-items-center px-2">
                                                <span class="d-block p-1 bg-primary-subtle rounded me-1"></span>
                                                <span class="d-block p-1 pb-0 px-2 bg-primary-subtle ms-auto"></span>
                                                <span class="d-block p-1 pb-0 px-2 bg-primary-subtle"></span>
                                            </span>
                                            <span class="d-flex gap-1 h-100 p-1 px-2">
                                                <span class="flex-shrink-0">
                                                    <span class="bg-light d-flex h-100 flex-column gap-1 p-1">
                                                        <span class="d-block p-1 px-2 pb-0 bg-primary-subtle"></span>
                                                        <span class="d-block p-1 px-2 pb-0 bg-primary-subtle"></span>
                                                        <span class="d-block p-1 px-2 pb-0 bg-primary-subtle"></span>
                                                    </span>
                                                </span>
                                            </span>
                                            <span class="bg-light d-block p-1 mt-auto px-2"></span>
                                        </span>
                                    </label>
                                </div>
                                <h5 class="fs-13 text-center mt-2">Detached</h5>
                            </div>
                        </div>
                    </div>
                    <div id="sidebar-color">
                        <h6 class="mt-4 mb-0 fw-semibold text-uppercase">Sidebar Color</h6>
                        <p class="text-muted">Choose a color of Sidebar.</p>

                        <div class="row">
                            <div class="col-4">
                                <div class="form-check sidebar-setting card-radio" data-bs-toggle="collapse" data-bs-target="#collapseBgGradient.show">
                                    <input class="form-check-input" type="radio" name="data-sidebar" id="sidebar-color-light" value="light">
                                    <label class="form-check-label p-0 avatar-md w-100 material-shadow" for="sidebar-color-light">
                                        <span class="d-flex gap-1 h-100">
                                            <span class="flex-shrink-0">
                                                <span class="bg-white border-end d-flex h-100 flex-column gap-1 p-1">
                                                    <span class="d-block p-1 px-2 bg-primary-subtle rounded mb-2"></span>
                                                    <span class="d-block p-1 px-2 pb-0 bg-primary-subtle"></span>
                                                    <span class="d-block p-1 px-2 pb-0 bg-primary-subtle"></span>
                                                    <span class="d-block p-1 px-2 pb-0 bg-primary-subtle"></span>
                                                </span>
                                            </span>
                                            <span class="flex-grow-1">
                                                <span class="d-flex h-100 flex-column">
                                                    <span class="bg-light d-block p-1"></span>
                                                    <span class="bg-light d-block p-1 mt-auto"></span>
                                                </span>
                                            </span>
                                        </span>
                                    </label>
                                </div>
                                <h5 class="fs-13 text-center mt-2">Light</h5>
                            </div>
                            <div class="col-4">
                                <div class="form-check sidebar-setting card-radio" data-bs-toggle="collapse" data-bs-target="#collapseBgGradient.show">
                                    <input class="form-check-input" type="radio" name="data-sidebar" id="sidebar-color-dark" value="dark">
                                    <label class="form-check-label p-0 avatar-md w-100 material-shadow" for="sidebar-color-dark">
                                        <span class="d-flex gap-1 h-100">
                                            <span class="flex-shrink-0">
                                                <span class="bg-primary d-flex h-100 flex-column gap-1 p-1">
                                                    <span class="d-block p-1 px-2 bg-white bg-opacity-10 rounded mb-2"></span>
                                                    <span class="d-block p-1 px-2 pb-0 bg-white bg-opacity-10"></span>
                                                    <span class="d-block p-1 px-2 pb-0 bg-white bg-opacity-10"></span>
                                                    <span class="d-block p-1 px-2 pb-0 bg-white bg-opacity-10"></span>
                                                </span>
                                            </span>
                                            <span class="flex-grow-1">
                                                <span class="d-flex h-100 flex-column">
                                                    <span class="bg-light d-block p-1"></span>
                                                    <span class="bg-light d-block p-1 mt-auto"></span>
                                                </span>
                                            </span>
                                        </span>
                                    </label>
                                </div>
                                <h5 class="fs-13 text-center mt-2">Dark</h5>
                            </div>
                            <div class="col-4">
                                <button class="btn btn-link avatar-md w-100 p-0 overflow-hidden border collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseBgGradient" aria-expanded="false" aria-controls="collapseBgGradient">
                                    <span class="d-flex gap-1 h-100">
                                        <span class="flex-shrink-0">
                                            <span class="bg-vertical-gradient d-flex h-100 flex-column gap-1 p-1">
                                                <span class="d-block p-1 px-2 bg-white bg-opacity-10 rounded mb-2"></span>
                                                <span class="d-block p-1 px-2 pb-0 bg-white bg-opacity-10"></span>
                                                <span class="d-block p-1 px-2 pb-0 bg-white bg-opacity-10"></span>
                                                <span class="d-block p-1 px-2 pb-0 bg-white bg-opacity-10"></span>
                                            </span>
                                        </span>
                                        <span class="flex-grow-1">
                                            <span class="d-flex h-100 flex-column">
                                                <span class="bg-light d-block p-1"></span>
                                                <span class="bg-light d-block p-1 mt-auto"></span>
                                            </span>
                                        </span>
                                    </span>
                                </button>
                                <h5 class="fs-13 text-center mt-2">Gradient</h5>
                            </div>
                        </div>
                        <!-- end row -->

                        <div class="collapse" id="collapseBgGradient">
                            <div class="d-flex gap-2 flex-wrap img-switch p-2 px-3 bg-light rounded">

                                <div class="form-check sidebar-setting card-radio">
                                    <input class="form-check-input" type="radio" name="data-sidebar" id="sidebar-color-gradient" value="gradient">
                                    <label class="form-check-label p-0 avatar-xs rounded-circle" for="sidebar-color-gradient">
                                        <span class="avatar-title rounded-circle bg-vertical-gradient"></span>
                                    </label>
                                </div>
                                <div class="form-check sidebar-setting card-radio">
                                    <input class="form-check-input" type="radio" name="data-sidebar" id="sidebar-color-gradient-2" value="gradient-2">
                                    <label class="form-check-label p-0 avatar-xs rounded-circle" for="sidebar-color-gradient-2">
                                        <span class="avatar-title rounded-circle bg-vertical-gradient-2"></span>
                                    </label>
                                </div>
                                <div class="form-check sidebar-setting card-radio">
                                    <input class="form-check-input" type="radio" name="data-sidebar" id="sidebar-color-gradient-3" value="gradient-3">
                                    <label class="form-check-label p-0 avatar-xs rounded-circle" for="sidebar-color-gradient-3">
                                        <span class="avatar-title rounded-circle bg-vertical-gradient-3"></span>
                                    </label>
                                </div>
                                <div class="form-check sidebar-setting card-radio">
                                    <input class="form-check-input" type="radio" name="data-sidebar" id="sidebar-color-gradient-4" value="gradient-4">
                                    <label class="form-check-label p-0 avatar-xs rounded-circle" for="sidebar-color-gradient-4">
                                        <span class="avatar-title rounded-circle bg-vertical-gradient-4"></span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div id="sidebar-img">
                        <h6 class="mt-4 mb-0 fw-semibold text-uppercase">Sidebar Images</h6>
                        <p class="text-muted">Choose a image of Sidebar.</p>

                        <div class="d-flex gap-2 flex-wrap img-switch">
                            <div class="form-check sidebar-setting card-radio">
                                <input class="form-check-input" type="radio" name="data-sidebar-image" id="sidebarimg-none" value="none">
                                <label class="form-check-label p-0 avatar-sm h-auto" for="sidebarimg-none">
                                    <span class="avatar-md w-auto bg-light d-flex align-items-center justify-content-center">
                                        <i class="ri-close-fill fs-20"></i>
                                    </span>
                                </label>
                            </div>

                            <div class="form-check sidebar-setting card-radio">
                                <input class="form-check-input" type="radio" name="data-sidebar-image" id="sidebarimg-01" value="img-1">
                                <label class="form-check-label p-0 avatar-sm h-auto" for="sidebarimg-01">
                                    <img src="assets/images/sidebar/img-1.jpg" alt="" class="avatar-md w-auto object-fit-cover">
                                </label>
                            </div>	

                            <div class="form-check sidebar-setting card-radio">
                                <input class="form-check-input" type="radio" name="data-sidebar-image" id="sidebarimg-02" value="img-2">
                                <label class="form-check-label p-0 avatar-sm h-auto" for="sidebarimg-02">
                                    <img src="assets/images/sidebar/img-2.jpg" alt="" class="avatar-md w-auto object-fit-cover">
                                </label>
                            </div>
                            <div class="form-check sidebar-setting card-radio">
                                <input class="form-check-input" type="radio" name="data-sidebar-image" id="sidebarimg-03" value="img-3">
                                <label class="form-check-label p-0 avatar-sm h-auto" for="sidebarimg-03">
                                    <img src="assets/images/sidebar/img-3.jpg" alt="" class="avatar-md w-auto object-fit-cover">
                                </label>
                            </div>
                            <div class="form-check sidebar-setting card-radio">
                                <input class="form-check-input" type="radio" name="data-sidebar-image" id="sidebarimg-04" value="img-4">
                                <label class="form-check-label p-0 avatar-sm h-auto" for="sidebarimg-04">
                                    <img src="assets/images/sidebar/img-4.jpg" alt="" class="avatar-md w-auto object-fit-cover">
                                </label>
                            </div>
                        </div>
                    </div>

                    <div id="sidebar-color">
                        <h6 class="mt-4 mb-0 fw-semibold text-uppercase">Primary Color</h6>
                        <p class="text-muted">Choose a color of Primary.</p>

                        <div class="d-flex flex-wrap gap-2">
                            <div class="form-check sidebar-setting card-radio">
                                <input class="form-check-input" type="radio" name="data-theme-colors" id="themeColor-01" value="default">
                                <label class="form-check-label avatar-xs p-0" for="themeColor-01"></label>
                            </div>
                            <div class="form-check sidebar-setting card-radio">
                                <input class="form-check-input" type="radio" name="data-theme-colors" id="themeColor-02" value="green">
                                <label class="form-check-label avatar-xs p-0" for="themeColor-02"></label>
                            </div>
                            <div class="form-check sidebar-setting card-radio">
                                <input class="form-check-input" type="radio" name="data-theme-colors" id="themeColor-03" value="purple">
                                <label class="form-check-label avatar-xs p-0" for="themeColor-03"></label>
                            </div>
                            <div class="form-check sidebar-setting card-radio">
                                <input class="form-check-input" type="radio" name="data-theme-colors" id="themeColor-04" value="blue">
                                <label class="form-check-label avatar-xs p-0" for="themeColor-04"></label>
                            </div>
                        </div>
                    </div>

                    <div id="preloader-menu">
                        <h6 class="mt-4 mb-0 fw-semibold text-uppercase">Preloader</h6>
                        <p class="text-muted">Choose a preloader.</p>
                    
                        <div class="row">
                            <div class="col-4">
                                <div class="form-check sidebar-setting card-radio">
                                    <input class="form-check-input" type="radio" name="data-preloader" id="preloader-view-custom" value="enable">
                                    <label class="form-check-label p-0 avatar-md w-100 material-shadow" for="preloader-view-custom">
                                        <span class="d-flex gap-1 h-100">
                                            <span class="flex-shrink-0">
                                                <span class="bg-light d-flex h-100 flex-column gap-1 p-1">
                                                    <span class="d-block p-1 px-2 bg-primary-subtle rounded mb-2"></span>
                                                    <span class="d-block p-1 px-2 pb-0 bg-primary-subtle"></span>
                                                    <span class="d-block p-1 px-2 pb-0 bg-primary-subtle"></span>
                                                    <span class="d-block p-1 px-2 pb-0 bg-primary-subtle"></span>
                                                </span>
                                            </span>
                                            <span class="flex-grow-1">
                                                <span class="d-flex h-100 flex-column">
                                                    <span class="bg-light d-block p-1"></span>
                                                    <span class="bg-light d-block p-1 mt-auto"></span>
                                                </span>
                                            </span>
                                        </span>
                                        <!-- <div id="preloader"> -->
                                        <div id="status" class="d-flex align-items-center justify-content-center">
                                            <div class="spinner-border text-primary avatar-xxs m-auto" role="status">
                                                <span class="visually-hidden">Loading...</span>
                                            </div>
                                        </div>
                                        <!-- </div> -->
                                    </label>
                                </div>
                                <h5 class="fs-13 text-center mt-2">Enable</h5>
                            </div>
                            <div class="col-4">
                                <div class="form-check sidebar-setting card-radio">
                                    <input class="form-check-input" type="radio" name="data-preloader" id="preloader-view-none" value="disable">
                                    <label class="form-check-label p-0 avatar-md w-100 material-shadow" for="preloader-view-none">
                                        <span class="d-flex gap-1 h-100">
                                            <span class="flex-shrink-0">
                                                <span class="bg-light d-flex h-100 flex-column gap-1 p-1">
                                                    <span class="d-block p-1 px-2 bg-primary-subtle rounded mb-2"></span>
                                                    <span class="d-block p-1 px-2 pb-0 bg-primary-subtle"></span>
                                                    <span class="d-block p-1 px-2 pb-0 bg-primary-subtle"></span>
                                                    <span class="d-block p-1 px-2 pb-0 bg-primary-subtle"></span>
                                                </span>
                                            </span>
                                            <span class="flex-grow-1">
                                                <span class="d-flex h-100 flex-column">
                                                    <span class="bg-light d-block p-1"></span>
                                                    <span class="bg-light d-block p-1 mt-auto"></span>
                                                </span>
                                            </span>
                                        </span>
                                    </label>
                                </div>
                                <h5 class="fs-13 text-center mt-2">Disable</h5>
                            </div>
                        </div>
                    
                    </div>
                    <!-- end preloader-menu -->

                    <div id="body-img" style="display: none;">
                        <h6 class="mt-4 mb-0 fw-semibold text-uppercase">Background Image</h6>
                        <p class="text-muted">Choose a body background image.</p>
                
                        <div class="row">
                            <div class="col-4">
                                <div class="form-check sidebar-setting card-radio">
                                    <input class="form-check-input" type="radio" name="data-body-image" id="body-img-none" value="none">
                                    <label class="form-check-label p-0 avatar-md w-100" data-body-image="none" for="body-img-none">
                                        <span class="d-flex gap-1 h-100">
                                            <span class="flex-shrink-0">
                                                <span class="bg-light d-flex h-100 flex-column gap-1 p-1">
                                                    <span class="d-block p-1 px-2 bg-primary-subtle rounded mb-2"></span>
                                                    <span class="d-block p-1 px-2 pb-0 bg-primary-subtle"></span>
                                                    <span class="d-block p-1 px-2 pb-0 bg-primary-subtle"></span>
                                                    <span class="d-block p-1 px-2 pb-0 bg-primary-subtle"></span>
                                                </span>
                                            </span>
                                            <span class="flex-grow-1">
                                                <span class="d-flex h-100 flex-column">
                                                    <span class="bg-light d-block p-1"></span>
                                                    <span class="bg-light d-block p-1 mt-auto"></span>
                                                </span>
                                            </span>
                                        </span>
                                    </label>
                                </div>
                                <h5 class="fs-13 text-center mt-2">None</h5>
                            </div>
                            <!-- end col -->
                            <div class="col-4">
                                <div class="form-check sidebar-setting card-radio">
                                    <input class="form-check-input" type="radio" name="data-body-image" id="body-img-one" value="img-1">
                                    <label class="form-check-label p-0 avatar-md w-100" data-body-image="img-1" for="body-img-one">
                                    </label>
                                </div>
                                <h5 class="fs-13 text-center mt-2">One</h5>
                            </div>
                            <!-- end col -->
                
                            <div class="col-4">
                                <div class="form-check sidebar-setting card-radio">
                                    <input class="form-check-input" type="radio" name="data-body-image" id="body-img-two" value="img-2">
                                    <label class="form-check-label p-0 avatar-md w-100" data-body-image="img-2" for="body-img-two">
                                    </label>
                                </div>
                                <h5 class="fs-13 text-center mt-2">Two</h5>
                            </div>
                            <!-- end col -->
                
                            <div class="col-4">
                                <div class="form-check sidebar-setting card-radio">
                                    <input class="form-check-input" type="radio" name="data-body-image" id="body-img-three" value="img-3">
                                    <label class="form-check-label p-0 avatar-md w-100" data-body-image="img-3" for="body-img-three">
                                    </label>
                                </div>
                                <h5 class="fs-13 text-center mt-2">Three</h5>
                            </div>
                            <!-- end col -->
                        </div>
                        <!-- end row -->
                    </div>

                </div>
            </div>

        </div>
        <div class="offcanvas-footer border-top p-3 text-center">
            <div class="row">
                <div class="col-6">
                    <button type="button" class="btn btn-light w-100" id="reset-layout">Reset</button>
                </div>
                <div class="col-6">
                    <a href="https://1.envato.market/velzon-admin" target="_blank" class="btn btn-primary w-100">Buy Now</a>
                </div>
            </div>
        </div>
    </div>

    <!-- JAVASCRIPT -->
    <script src="assets/libs/bootstrap/js/bootstrap.bundle.min.js"></script>
    <script src="assets/libs/simplebar/simplebar.min.js"></script>
    <script src="assets/libs/node-waves/waves.min.js"></script>
    <script src="assets/libs/feather-icons/feather.min.js"></script>
    <script src="assets/js/pages/plugins/lord-icon-2.1.0.js"></script>
    <script src="assets/js/plugins.js"></script>

    <!-- App js -->
    <script src="assets/js/app.js"></script>
</body>


<!-- Mirrored from themesbrand.com/velzon/html/master/icons-crypto.html by HTTrack Website Copier/3.x [XR&CO'2014], Fri, 23 Aug 2024 16:39:47 GMT -->
</html>