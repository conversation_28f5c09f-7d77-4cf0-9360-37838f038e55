var mymap=<PERSON>.map("leaflet-map").setView([51.505,-.09],13),markermap=(<PERSON><PERSON>tileLayer("https://api.mapbox.com/styles/v1/{id}/tiles/{z}/{x}/{y}?access_token=pk.eyJ1IjoibWFwYm94IiwiYSI6ImNpejY4NXVycTA2emYycXBndHRqcmZ3N3gifQ.rJcFIG214AriISLbB6B5aw",{maxZoom:18,attribution:'Map data &copy; <a href="https://www.openstreetmap.org/">OpenStreetMap</a> contributors, <a href="https://creativecommons.org/licenses/by-sa/2.0/">CC-BY-SA</a>, Imagery © <a href="https://www.mapbox.com/">Mapbox</a>',id:"mapbox/streets-v11",tileSize:512,zoomOffset:-1}).addTo(mymap),<PERSON><PERSON>map("leaflet-map-marker").setView([51.505,-.09],13)),popupmap=(L<PERSON>tileLayer("https://api.mapbox.com/styles/v1/{id}/tiles/{z}/{x}/{y}?access_token=pk.eyJ1IjoibWFwYm94IiwiYSI6ImNpejY4NXVycTA2emYycXBndHRqcmZ3N3gifQ.rJcFIG214AriISLbB6B5aw",{maxZoom:18,attribution:'Map data &copy; <a href="https://www.openstreetmap.org/">OpenStreetMap</a> contributors, <a href="https://creativecommons.org/licenses/by-sa/2.0/">CC-BY-SA</a>, Imagery © <a href="https://www.mapbox.com/">Mapbox</a>',id:"mapbox/streets-v11",tileSize:512,zoomOffset:-1}).addTo(markermap),L.marker([51.5,-.09]).addTo(markermap),L.circle([51.508,-.11],{color:"#0ab39c",fillColor:"#0ab39c",fillOpacity:.5,radius:500}).addTo(markermap),L.polygon([[51.509,-.08],[51.503,-.06],[51.51,-.047]],{color:"#405189",fillColor:"#405189"}).addTo(markermap),L.map("leaflet-map-popup").setView([51.505,-.09],13)),popup=(L.tileLayer("https://api.mapbox.com/styles/v1/{id}/tiles/{z}/{x}/{y}?access_token=pk.eyJ1IjoibWFwYm94IiwiYSI6ImNpejY4NXVycTA2emYycXBndHRqcmZ3N3gifQ.rJcFIG214AriISLbB6B5aw",{maxZoom:18,attribution:'Map data &copy; <a href="https://www.openstreetmap.org/">OpenStreetMap</a> contributors, <a href="https://creativecommons.org/licenses/by-sa/2.0/">CC-BY-SA</a>, Imagery © <a href="https://www.mapbox.com/">Mapbox</a>',id:"mapbox/streets-v11",tileSize:512,zoomOffset:-1}).addTo(popupmap),L.marker([51.5,-.09]).addTo(popupmap).bindPopup("<b>Hello world!</b><br />I am a popup.").openPopup(),L.circle([51.508,-.11],500,{color:"#f06548",fillColor:"#f06548",fillOpacity:.5}).addTo(popupmap).bindPopup("I am a circle."),L.polygon([[51.509,-.08],[51.503,-.06],[51.51,-.047]],{color:"#405189",fillColor:"#405189"}).addTo(popupmap).bindPopup("I am a polygon."),L.popup()),customiconsmap=L.map("leaflet-map-custom-icons").setView([51.5,-.09],13),LeafIcon=(L.tileLayer("https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png",{attribution:'&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'}).addTo(customiconsmap),L.Icon.extend({options:{iconSize:[45,45],iconAnchor:[22,94],popupAnchor:[-3,-76]}})),greenIcon=new LeafIcon({iconUrl:"assets/images/logo-sm.png"}),interactivemap=(L.marker([51.5,-.09],{icon:greenIcon}).addTo(customiconsmap),L.map("leaflet-map-interactive-map").setView([37.8,-96],4));function getColor(e){return 1e3<e?"#405189":500<e?"#516194":200<e?"#63719E":100<e?"#7480A9":50<e?"#8590B4":20<e?"#97A0BF":"#A8B0C9"}function style(e){return{weight:2,opacity:1,color:"white",dashArray:"3",fillOpacity:.7,fillColor:getColor(e.properties.density)}}L.tileLayer("https://api.mapbox.com/styles/v1/{id}/tiles/{z}/{x}/{y}?access_token=pk.eyJ1IjoibWFwYm94IiwiYSI6ImNpejY4NXVycTA2emYycXBndHRqcmZ3N3gifQ.rJcFIG214AriISLbB6B5aw",{maxZoom:18,attribution:'Map data &copy; <a href="https://www.openstreetmap.org/">OpenStreetMap</a> contributors, <a href="https://creativecommons.org/licenses/by-sa/2.0/">CC-BY-SA</a>, Imagery © <a href="https://www.mapbox.com/">Mapbox</a>',id:"mapbox/light-v9",tileSize:512,zoomOffset:-1}).addTo(interactivemap);var geojson=L.geoJson(statesData,{style:style}).addTo(interactivemap),cities=L.layerGroup(),mbAttr=(L.marker([39.61,-105.02]).bindPopup("This is Littleton, CO.").addTo(cities),L.marker([39.74,-104.99]).bindPopup("This is Denver, CO.").addTo(cities),L.marker([39.73,-104.8]).bindPopup("This is Aurora, CO.").addTo(cities),L.marker([39.77,-105.23]).bindPopup("This is Golden, CO.").addTo(cities),'Map data &copy; <a href="https://www.openstreetmap.org/">OpenStreetMap</a> contributors, <a href="https://creativecommons.org/licenses/by-sa/2.0/">CC-BY-SA</a>, Imagery © <a href="https://www.mapbox.com/">Mapbox</a>'),mbUrl="https://api.mapbox.com/styles/v1/{id}/tiles/{z}/{x}/{y}?access_token=pk.eyJ1IjoibWFwYm94IiwiYSI6ImNpejY4NXVycTA2emYycXBndHRqcmZ3N3gifQ.rJcFIG214AriISLbB6B5aw",grayscale=L.tileLayer(mbUrl,{id:"mapbox/light-v9",tileSize:512,zoomOffset:-1,attribution:mbAttr}),streets=L.tileLayer(mbUrl,{id:"mapbox/streets-v11",tileSize:512,zoomOffset:-1,attribution:mbAttr}),layergroupcontrolmap=L.map("leaflet-map-group-control",{center:[39.73,-104.99],zoom:10,layers:[streets,cities]}),baseLayers={Grayscale:grayscale,Streets:streets},overlays={Cities:cities};L.control.layers(baseLayers,overlays).addTo(layergroupcontrolmap);